package com.common.utils.httpUtils;

/**
 * 通过 OpenAPI 进行 HTTP 请求时，如果发生异常，则以该异常进行抛出。
 * 
 * <AUTHOR>
 */
public class MyHttpException extends MyException {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 9083125639717368688L;
	
	/** HTTP请求出错时，服务器返回的错误状态码 */
	private final int mStatusCode;
	
	/**
	 * 构造函数。
	 * 
	 * @param message
	 *            HTTP请求出错时，服务器返回的字符串
	 * @param statusCode
	 *            HTTP请求出错时，服务器返回的错误状态码
	 */
	public MyHttpException(String message, int statusCode) {
		super(message);
		mStatusCode = statusCode;
	}
	
	/**
	 * HTTP请求出错时，服务器返回的错误状态码。
	 * 
	 * @return 服务器返回的错误状态码
	 */
	public int getStatusCode() {
		return mStatusCode;
	}
}
