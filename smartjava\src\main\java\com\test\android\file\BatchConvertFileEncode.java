package com.test.android.file;

import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量转换文件编码
 *
 * @date 2014-10-23
 *
 * <AUTHOR>
 */
public class BatchConvertFileEncode {
    /**
     * 获取文件或文件夹 不存在则创建
     *
     * @param path
     *            文件或文件夹路径
     * @return 已有/新创建的文件
     * @throws IOException
     *             可能产生的异常
     */
    public static File getFile(String path) throws IOException {
        File file = new File(path);
        if (file.isDirectory()) {
            if (!file.exists()) {
                file.mkdirs();
            }
        } else {
            // 判断目标文件所在的目录是否存在
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            if (!file.exists()) {
                file.createNewFile();
            }
        }
        return file;
    }
    /**
     * 递归查找指定后缀名的文件
     *
     * @param folder
     *            目标文件夹
     * @param suffix
     *            目标后缀名
     * @return 找到的文件集合
     */
    public static List<File> searchFile(File folder, final String suffix) {
        List<File> result = new ArrayList<File>();
        File[] subFolders = folder.listFiles(new FileFilter() {// 运用内部匿名类获得文件
            @Override
            public boolean accept(File pathname) {// 实现FileFilter类的accept方法
                if (pathname.isDirectory()
                        || (pathname.isFile() && pathname.getName().toLowerCase()
                        .endsWith(suffix.toLowerCase())))// 根据文件后缀名过滤
                {
                    return true;
                }
                return false;
            }
        });
        if (subFolders != null) {
            for (File file : subFolders) {
                if (file.isFile()) {
                    // 如果是文件则将文件添加到结果列表中
                    result.add(file);
                } else {
                    // 如果是文件夹，则递归调用本方法，然后把所有的文件加到结果列表中
                    result.addAll(searchFile(file, suffix));
                }
            }
        }
        return result;
    }
    /**
     * 单个文件转换编码
     *
     * @param file
     *            要转换的文件
     * @param tarFile
     *            转换后的文件
     * @param charset
     *            转换前的编码
     * @param tarCharset
     *            转换后的编码
     * @throws IOException
     *             可能出现的异常
     */
    public static void convertFileEncode(File file, File tarFile, String charset, String tarCharset)
            throws IOException {
        InputStreamReader reader = null;
        OutputStreamWriter writer = null;
        int length;
        char[] b = new char[3 * 1024];
        try {
            // 打开文件输出流
            reader = new InputStreamReader(new FileInputStream(file), charset);
            writer = new OutputStreamWriter(new FileOutputStream(tarFile), tarCharset);
            while ((length = reader.read(b)) != -1) {
                writer.write(b, 0, length);
                writer.flush();
            }
        } finally {
            // 关闭文件流
            if (reader != null) {
                reader.close();
            }
            if (writer != null) {
                writer.close();
            }
        }
    }
    /**
     * 根据文件夹批量转换编码
     *
     * @param folder
     *            要转换的文件夹
     * @param tarFolder
     *            输出文件夹
     * @param charset
     *            转换前的编码
     * @param tarCharset
     *            转换后的编码
     * @param suffix
     *            要转换的文件后缀名
     * @throws IOException
     *             可能出现的异常
     */
    public static void convertFileEncodeByFolder(String folder, String tarFolder, String charset,
                                                 String tarCharset, String suffix) throws IOException {
        String relTar = null;
        List<File> result = searchFile(new File(folder), suffix);// 调用方法获得文件数组
        System.out.println("找到 " + result.size() + " 个需要转换的文件");
        // 文件尾部处理
        if (!folder.endsWith("/") && !folder.endsWith("\\")) {
            folder += File.separator;
        }
        if (!tarFolder.endsWith("/") && !tarFolder.endsWith("\\")) {
            tarFolder += File.separator;
        }
        for (File file : result) {
            // 使目标文件夹目录层次与源文件夹对应
            relTar = tarFolder + file.getAbsolutePath().replace(folder.replace("/", "\\"),"");
            convertFileEncode(file, getFile(relTar), charset, tarCharset);
        }
        System.out.println("转换成功!");
    }
    public static void main(String[] args) {
        try {
            convertFileEncodeByFolder("E:\\Studio_WorkPlace\\TestAndroid\\app\\src\\main\\java", "E:\\Studio_WorkPlace\\TestAndroid\\app\\src\\main\\java2", "GB2312", "utf-8", ".java");
        } catch (IOException e) {
            e.printStackTrace();
        }

        /*try {
            convertFileEncode(new File("E:\\Studio_WorkPlace\\LauncherCopy\\app\\src\\main\\java\\com\\yulong\\android\\launcher3\\PagedView.java"),
                    new File("E:\\Studio_WorkPlace\\LauncherCopy\\app\\src\\main\\java\\com\\yulong\\android\\launcher3\\PagedView.java"),"GB2312","utf-8");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
        }*/
    }
}