package com.common.utils.httpUtils;

import java.io.IOException;
import java.util.ArrayList;

import android.os.Handler;
import android.os.Message;

public class HttpOpenApi {
	
	/** 用于转发回调函数的消息 */
	private static final int MSG_ON_COMPLETE = 1;
	private static final int MSG_ON_IOEXCEPTION = 2;
	private static final int MSG_ON_ERROR = 3;
	
	/** POST 请求方式 */
	protected static final String HTTPMETHOD_POST = "POST";
	/** GET 请求方式 */
	protected static final String HTTPMETHOD_GET = "GET";
	
	/** 异步请求回调接口 */
	private RequestListener mRequestListener;
	
	public HttpOpenApi() {
		super();
	}
	
	/**
	 * HTTP 异步请求。
	 * 
	 * @param url
	 *        请求的地址
	 * @param params
	 *        请求的参数
	 * @param httpMethod
	 *        请求方法
	 * @param listener
	 *        请求后的回调接口
	 */
	protected void request(String url, HttpParameters params, String httpMethod, RequestListener listener, String encode, String decode, ArrayList<MyHeader> headerList) {
		mRequestListener = listener;
		
		// 异步请求
		// params.add(KEY_ACCESS_TOKEN, mAccessToken.getAccessToken());
		AsyncHttpRunner.request(url, params, httpMethod, mInternalListener, encode, decode, headerList);
	}
	
	/**
	 * 该 Handler 用于将后台线程回调转发到 UI 线程。
	 */
	private Handler mHandler = new Handler() {
		@Override
		public void handleMessage(Message msg) {
			if (null == mRequestListener) {
				return;
			}
			switch (msg.what) {
				case MSG_ON_COMPLETE:
					mRequestListener.onComplete((String) msg.obj);
					break;
				
				case MSG_ON_IOEXCEPTION:
					mRequestListener.onIOException((IOException) msg.obj);
					break;
				
				case MSG_ON_ERROR:
					mRequestListener.onError((MyException) msg.obj);
					break;
				
				default:
					break;
			}
		}
	};
	
	/**
	 * 请注意：默认情况下，{@link RequestListener} 对应的回调是运行在后台线程中的， 因此，需要使用 Handler
	 * 来配合更新UI。
	 */
	private RequestListener mInternalListener = new RequestListener() {
		@Override
		public void onComplete(String response) {
			mHandler.obtainMessage(MSG_ON_COMPLETE, response).sendToTarget();
		}
		
		@Override
		public void onIOException(IOException e) {
			mHandler.obtainMessage(MSG_ON_IOEXCEPTION, e).sendToTarget();
		}
		
		@Override
		public void onError(MyException e) {
			mHandler.obtainMessage(MSG_ON_ERROR, e).sendToTarget();
		}
	};
}
