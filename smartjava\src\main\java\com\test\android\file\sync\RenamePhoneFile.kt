package com.test.android.file.sync

import com.bingo.core.Logs
import java.io.File

import java.text.SimpleDateFormat
import java.util.Date
import kotlin.system.exitProcess

/**
 * 从手机导出的图片/视频/录音等文件，把日期放到文件名的最前面，这样在听的时候，就可以按照时间顺序;
 * 对同一个目录，需要多次执行此程序，直到提示已处理的文件数量为0为止;
 *
 * To Rename File Names List:
 */
fun main() {
    val fileList = mutableListOf<String>()

    // val mediaDir = File("F:\\Temp\\Download")
    val mediaDir = File("F:\\Temp\\Download\\Temp")
    // val mediaDir = File("F:\\Temp\\Share\\Upload\\Humor")

    formatFileDateName(mediaDir, fileList)
    println("The Count of rename files: " + fileList.size)
}


fun formatFileDateName(directory: File, fileList: MutableList<String>) {
    if (directory.isDirectory) {
        val files = directory.listFiles()
        files?.forEach { file ->
            if (file.isDirectory) {
                formatFileDateName(file, fileList)
            } else {
                val fileName = file.name
                // 打印所有遍历的文件
                // Logs.i("------${file.path}------")

                if (fileName.startsWith(".")) {
                    return@forEach
                }

                var newFileName = ""
                try {
                    newFileName = moveTimeStampToFirst(fileName)
                    newFileName = convertTimeFormat(newFileName)
                    newFileName = convertTimeFormat2(newFileName)
                    newFileName = convertTimeFormat3(newFileName)
                    newFileName = convertTimeFormat4(newFileName)
                    newFileName = renamePrefix(newFileName)
                    newFileName = removeHorizontalLine(newFileName)
                    newFileName = removeHorizontalLine1(newFileName)
                    newFileName = removeHorizontalLine2(newFileName)
                    newFileName = removeHorizontalLine21(newFileName)
                    newFileName = removeHorizontalLine22(newFileName)
                    newFileName = removeHorizontalLine3(newFileName)
                    newFileName = removeHorizontalLine4(newFileName)
                    newFileName = removeHorizontalLine5(newFileName)
                    newFileName = removeHorizontalLine6(newFileName)
                    newFileName = insertUnderLine(newFileName)
                    newFileName = insertUnderLine2(newFileName)
                    newFileName = insertUnderLine3(newFileName)
                } catch (exception: Exception) {
                    Logs.i("------${file.path}------")
                    Logs.i("Exception: >>>>>> $fileName --> $newFileName <<<<<<")
                    Logs.i(exception.toString())
                    exitProcess(0)
                }

                if (!fileName.equals(newFileName)) {
                    Logs.i("$fileName --> $newFileName")
                    val newFile = File(directory.absolutePath + File.separator + newFileName)
                    file.renameTo(newFile)
                    fileList.add(newFile.name)
                }
            }
        }
    }
}


/**
 *  IMG_20230718_173308.jpg --> 20230718_173308_IMG.jpg
 *  VID_20230718_173308.jpg --> 20230718_173308_VID.jpg
 *  video_20200509_135956.mp4 --> 20200509_135956_video.mp4
 *  Screenshot_20230718_173308.jpg --> 20230718_173308_Screenshot.jpg
 *  Snipaste_2024-06-15_02-08-23.jpg --> 20240615_020823_Snipaste.jpg
 *  PXL_20231207_070023670.MP.jpg --> 20231207_070023670.MP_PXL.jpg
 *  MTXX_20240818_115450424.jpg --> 20240818_115450424_MTXX.jpg
 *  TG-2023-07-20-153016135.mp4 --> 2023-07-20-153016135-TG.mp4
 *  .VID_20241025_211522.mp4 --> VID_20241025_211522.mp4
 */
fun renamePrefix(fileName: String): String {
    val parts = fileName.split(".")
    if (parts.size > 1) {
        val extension = parts[parts.size - 1]
        val removeSuffix = fileName.removeSuffix(".$extension")

        val noLineStrList = arrayOf("MTXX_MH", "MTXX_MR", "MTXX_PT", ".")
        noLineStrList.forEach {
            if (removeSuffix.startsWith(it)) {
                val removePrefix = removeSuffix.removePrefix(it)
                Logs.i()
                if (it == ".") {
                    return "$removePrefix.$extension"
                }
                return removePrefix + "_$it.$extension"
            }
        }

        val withUnderLineStrList = arrayOf("IMG", "VID", "video", "SAVE", "PANO", "MYXJ", "Screenshot", "PXL", "Snipaste", "MEITU", "MTXX", "tb_image_share")
        withUnderLineStrList.forEach {
            if (removeSuffix.startsWith(it + "_")) {
                val removePrefix = removeSuffix.removePrefix(it + "_")
                Logs.i()
                return removePrefix + "_$it.$extension"
            }
        }

        val withLevelLineStrList = arrayOf("TG")
        withLevelLineStrList.forEach {
            if (removeSuffix.startsWith("$it-")) {
                val removePrefix = removeSuffix.removePrefix("$it-")
                Logs.i()
                return "$removePrefix-$it.$extension"
            }
        }
    }
    return fileName
}

/**
 *  Bingo_1669978073369.mp3 --> 20221202_184753_369_Bingo.mp3
 */
fun convertTimeFormat(fileName: String): String {
    if (fileName.length <= 13 || !fileName.contains("_")) {
        return fileName
    }
    val parts = fileName.split(".")
    if (parts.size > 1) {
        val extension = parts[parts.size - 1]
        val timeStr = fileName.substring(fileName.lastIndexOf("_") + 1, fileName.lastIndexOf("."))
        if (timeStr.length == 13 && timeStr.all { it.isDigit() }) {
            Logs.i()
            val date = Date(timeStr.toLong())
            val sdf = SimpleDateFormat("yyyyMMdd_HHmmss_SSS")
            val formattedDateTime = sdf.format(date)
            return formattedDateTime + "_" + fileName.substring(0, fileName.lastIndexOf("_")).trimEnd() + ".$extension"
        }
    }
    return fileName
}

/**
 *  mmexport1704636319595.mp4 --> 20240107_220519_595_mmexport.mp4
 *  mmqrcode1719185320381.png --> 20240624_072840_381_mmqrcode.png
 *  mmscreenshot1733066008010.png --> 20241201_231328_010_mmscreenshot.png
 *  ECommerce1704636319595.png --> 20240107_220519_595_ECommerce.png
 *  tb_image_share_1718702888928.jpg.png --> 20240618_172808_tb_image_share.png
 */
fun convertTimeFormat2(fileName: String): String {
    val noLineStrList = arrayOf("mmexport", "mmqrcode", "mmscreenshot", "ECommerce", "image")
    noLineStrList.forEach {
        if (fileName.startsWith(it)) {
            val parts = fileName.split(".")
            val extension = parts[parts.size - 1]
            val timeStampStr = parts[0].removePrefix(it)
            if (!timeStampStr.all { it.isDigit() }) {
                return fileName
            }
            val date = Date(timeStampStr.toLong())
            val sdf = SimpleDateFormat("yyyyMMdd_HHmmss_SSS")
            val formattedDateTime = sdf.format(date)
            return formattedDateTime + "_$it.$extension"
        }
    }
    val withUnderLineStrList = arrayOf("tb_image_share")
    withUnderLineStrList.forEach {
        if (fileName.startsWith(it + "_")) {
            Logs.i()
            val parts = fileName.split(".")
            val extension = parts[parts.size - 1]
            val timeStampStr = parts[0].removePrefix(it + "_")
            val date = Date(timeStampStr.toLong())
            val sdf = SimpleDateFormat("yyyyMMdd_HHmmss_SSS")
            val formattedDateTime = sdf.format(date)
            return formattedDateTime + "_$it.$extension"
        }
    }
    return fileName
}

/**
 * 1706958689231.jpg --> 20240203_191129_231.jpg
 */
fun convertTimeFormat3(fileName: String): String {
    val parts = fileName.split(".")
    if (parts.size > 1) {
        val extension = parts[parts.size - 1]
        val timeStr = parts[0]
        if (timeStr.length != 13 || !timeStr.all { it.isDigit() }) {
            return fileName
        }
        Logs.i()
        val date = Date(timeStr.toLong())
        val sdf = SimpleDateFormat("yyyyMMdd_HHmmss_SSS")
        val formattedDateTime = sdf.format(date)
        return "$formattedDateTime.$extension"
    }
    return fileName
}

/**
 * 1534133069992_1502948582.jpg --> 20180813_120429_992_1502948582.jpg
 */
fun convertTimeFormat4(fileName: String): String {
    val parts = fileName.split(".")
    if (parts.size > 1) {
        // val extension = parts[parts.size - 1]
        val nameStr = parts[0]
        if (nameStr.split("_").size != 2) { // 匹配只有一个下划线的字符串
            return fileName
        }
        var timeStr = nameStr.split("_")[0]
        if (timeStr.length != 13 || !timeStr.all { it.isDigit() }) {
            return fileName
        }
        Logs.i()
        val date = Date(timeStr.toLong())
        val sdf = SimpleDateFormat("yyyyMMdd_HHmmss_SSS")
        val formattedDateTime = sdf.format(date)
        return "$formattedDateTime${fileName.removePrefix(timeStr)}"
    }
    return fileName
}


/**
 * Bingo(18600598523)_20240316185002.mp3 -->  20240316_185002_Bingo(18600598523).mp3
 */
fun moveTimeStampToFirst(fileName: String): String {
    val parts = fileName.split(".")
    if (parts.size > 1) {
        val extension = parts[parts.size - 1]
        val startIndex = fileName.lastIndexOf("_") + 1
        val endIndex = fileName.lastIndexOf(".")
        if (startIndex >= endIndex) {
            return fileName
        }
        val timeStr = fileName.substring(startIndex, endIndex)
        if (timeStr.length == 14 && timeStr.all { it.isDigit() } && fileName.lastIndexOf("_") > 0) {
            Logs.i()
            return timeStr.substring(0, 8) + "_" + timeStr.removePrefix(timeStr.substring(0, 8)) + "_" + fileName.substring(0, fileName.lastIndexOf("_")).trimEnd() + ".$extension"
        }
    }
    return fileName
}


/**
 * 20240307170758_13807068831(13807068831).mp3 --> 20240307_170758_13807068831(13807068831).mp3
 */
fun insertUnderLine(fileName: String): String {
    val parts = fileName.split("_")
    if (parts.size > 1) {
        val timeStampStr = parts[0]
        if (timeStampStr.startsWith("20") && timeStampStr.length == 14 && timeStampStr.all { it.isDigit() }) {
            Logs.i()
            return timeStampStr.substring(0, 8) + "_" + fileName.removePrefix(timeStampStr.substring(0, 8))
        }
    }
    return fileName
}


/**
 * 2024051515070181.jpg --> 20240515_150701_81.jpg
 */
fun insertUnderLine2(fileName: String): String {
    val parts = fileName.split(".")
    if (parts.size > 1) {
        if (parts[0].length == 16 && parts[0].all { it.isDigit() }) {
            if (parts[0].startsWith("20")) {
                Logs.i()
                return parts[0].substring(0, 8) + "_" + parts[0].substring(8, 14) + "_" + fileName.substring(14, fileName.length)
            }
        }
    }
    return fileName
}


/**
 * IMG20231024101658.jpg --> 20231024_101658_IMG.jpg
 */
fun insertUnderLine3(oldFileName: String): String {
    // 1. 提取文件名部分 (去除路径)
    val fileNameWithoutExtension = File(oldFileName).nameWithoutExtension

    val extension = File(oldFileName).extension

    // 2. 检查文件名是否符合 IMGyyyyMMddHHmmss 格式
    val regex = Regex("IMG(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})")
    val matchResult = regex.find(fileNameWithoutExtension)

    return if (matchResult != null) {
        // 3. 提取年月日时分秒
        val (year, month, day, hour, minute, second) = matchResult.destructured
        Logs.i()
        // 4. 构建新的文件名
        "${year}${month}${day}_${hour}${minute}${second}_IMG.${extension}"
    } else {
        // 5. 如果文件名格式不匹配，返回原始文件名 (或者抛出异常，根据你的需求)
        // println("文件名格式不匹配：$oldFileName")
        oldFileName  // 或者 throw IllegalArgumentException("文件名格式不匹配")
    }
}


/**
 * 2022-11-22-22-03-25-263_com.miui.home_Screenshot.jpg --> 20221122_220325-263_com.miui.home_Screenshot.jpg
 */
fun removeHorizontalLine(fileName: String): String {
    val parts = fileName.split("_")
    if (parts.size > 1) {
        val timeStampStr = parts[0]
        val timeStampParts = timeStampStr.split("-")
        if (timeStampParts.size == 7) {
            Logs.i()
            return timeStampParts[0] + timeStampParts[1] + timeStampParts[2] + "_" + timeStampParts[3] + timeStampParts[4] + timeStampParts[5] + "-" + timeStampParts[6] + fileName.removePrefix(
                parts[0]
            )
        }
    }
    return fileName
}

/**
 * 2013_07_12_11_12_18.jpg --> 20130712_111218.jpg
 */
fun removeHorizontalLine1(fileName: String): String {
    val parts = fileName.split(".")
    if (parts.size == 2) {
        if (parts[0].split("_").size != 6) {
            return fileName
        }
        val timeStr = parts[0].replace("_", "")
        if (timeStr.length == 14 && timeStr.all { it.isDigit() }) {
            if (timeStr.startsWith("20") || timeStr.startsWith("19")) {
                Logs.i()
                return timeStr.substring(0, 8) + "_" + timeStr.substring(8, 14) + "." + parts[1]
            }
        }
    }
    return fileName
}

/**
 * 2023-11-18-082229223-TG.mp4 -->  20231118_082229223-TG.mp4
 */
fun removeHorizontalLine2(fileName: String): String {
    val parts = fileName.split("-")
    if (parts.size > 4) {
        val yearStr = parts[0]
        val monthStr = parts[1]
        val dayStr = parts[2]
        if (yearStr.length == 4 && yearStr.all { it.isDigit() }
            && monthStr.length == 2 && monthStr.all { it.isDigit() }
            && dayStr.length == 2 && dayStr.all { it.isDigit() }) {
            Logs.i()
            return yearStr + monthStr + dayStr + "_" + fileName.substring(11, fileName.length)
        }
    }
    return fileName
}

/**
 * 2023-12-19_163221_cloud_video.mp4 -->  20231219_163221_cloud_video.mp4
 */
fun removeHorizontalLine21(fileName: String): String {
    val parts = fileName.split("-")
    if (parts.size == 3) {
        val yearStr = parts[0]
        val monthStr = parts[1]
        val dayStr = parts[2].substring(0, 2)
        if (yearStr.length == 4 && yearStr.all { it.isDigit() }
            && monthStr.length == 2 && monthStr.all { it.isDigit() }
            && dayStr.length == 2 && dayStr.all { it.isDigit() }) {
            Logs.i()
            return yearStr + monthStr + dayStr + "_" + fileName.substring(11, fileName.length)
        }
    }
    return fileName
}


/**
 *  20240615_02-08-23_Snipaste.png --> 20240615_020823_Snipaste.png
 */
fun removeHorizontalLine22(fileName: String): String {
    val parts = fileName.split("_")
    if (parts.size >= 3) {
        val ymdStr = parts[0]
        val timeStr = parts[1]
        val timeParts = timeStr.split("-")
        if (timeParts.size == 3) {
            val hourStr = timeParts[0]
            val minuteStr = timeParts[1]
            val secondStr = timeParts[2]
            if (hourStr.length == 2 && hourStr.all { it.isDigit() }
                && minuteStr.length == 2 && minuteStr.all { it.isDigit() }
                && secondStr.length == 2 && secondStr.all { it.isDigit() }) {
                Logs.i()
                return ymdStr + "_" + hourStr + minuteStr + secondStr + fileName.substring(17, fileName.length)
            }
        }
    }
    return fileName
}


/**
 * 2022-11-20_20-03-56_838.mp4  -->  20221120_200356_838.mp4
 */
fun removeHorizontalLine3(fileName: String): String {
    val parts = fileName.split("_")
    if (parts.size >= 3) {
        val dateStr = parts[0]
        val timeStr = parts[1]
        if (dateStr.length == 10 && timeStr.length == 8) {
            val dateStrParts = dateStr.split("-")
            if (dateStrParts.size >= 3) {
                val yearStr = dateStrParts[0]
                val monthStr = dateStrParts[1]
                val dayStr = dateStrParts[2]
                if (yearStr.length == 4 && yearStr.all { it.isDigit() }
                    && monthStr.length == 2 && monthStr.all { it.isDigit() }
                    && dayStr.length == 2 && dayStr.all { it.isDigit() }) {
                    val timeStrParts = timeStr.split("-")
                    if (timeStrParts.size >= 3) {
                        Logs.i()
                        val hourStr = timeStrParts[0]
                        val minuteStr = timeStrParts[1]
                        val secondStr = timeStrParts[2]
                        if (hourStr.length == 2 && hourStr.all { it.isDigit() }
                            && minuteStr.length == 2 && minuteStr.all { it.isDigit() }
                            && secondStr.length == 2 && secondStr.all { it.isDigit() }) {
                            return yearStr + monthStr + dayStr + "_" + hourStr + minuteStr + secondStr + "_" + fileName.removePrefix(parts[0] + "_" + parts[1] + "_")
                        }
                    }
                }
            }
        }
    }
    return fileName
}

/**
 * Screenrecorder-2024-03-31-19-49-48-248.mp4 --> 20240331_194948_248-Screenrecorder.mp4
 */
fun removeHorizontalLine4(fileName: String): String {
    val parts = fileName.split(".")
    if (parts.size > 1) {
        val extension = parts[parts.size - 1]
        val firstPart = parts[0]
        val removePrefix = firstPart.removePrefix("Screenrecorder-")
        val timeParts = removePrefix.split("-")
        if (timeParts.size == 7) {
            Logs.i()
            return timeParts[0] + timeParts[1] + timeParts[2] + "_" +
                    timeParts[3] + timeParts[4] + timeParts[5] + "_" + timeParts[6] + "-Screenrecorder" + ".$extension"
        }
    }
    return fileName
}

/**
 * Record_2024-06-08-01-18-52.mp4 --> 20240608_011852_Record.mp4
 */
fun removeHorizontalLine5(fileName: String): String {
    val parts = fileName.split(".")
    if (parts.size > 1) {
        val extension = parts[parts.size - 1]
        val firstPart = parts[0]
        val removePrefix = firstPart.removePrefix("Record_")
        val timeParts = removePrefix.split("-")
        if (timeParts.size == 6) {
            Logs.i()
            return timeParts[0] + timeParts[1] + timeParts[2] + "_" +
                    timeParts[3] + timeParts[4] + timeParts[5] + "-Record" + ".$extension"
        }
    }
    return fileName
}


/**
 *  2024-11-26_22-21-02.mp4 --> 20241126_222102.mp4
 */
fun removeHorizontalLine6(fileName: String): String {
    val parts = fileName.split(".")
    if (parts.size == 2) {
        if (parts[0].split("-").size == 5 && parts[0].split("_").size == 2) {
            Logs.i()
            val dateTimeStr = parts[0].replace("-", "").replace("_", "")
            if (dateTimeStr.length == 14 && dateTimeStr.all { it.isDigit() }) {
                return fileName.replace("-", "")
            }
        }
    }
    return fileName
}