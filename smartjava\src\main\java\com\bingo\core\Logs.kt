package com.bingo.core

// Logs For Java

import kotlin.text.isNullOrEmpty
import java.util.concurrent.atomic.AtomicInteger


/**
 * 精简为常用的三个日志方法，注意：在编译Release APK时，需要通过混淆，把所有日志方法的调用移除。
 */
object Logs {
    private const val ENABLE_LOG = true
    private const val ENABLE_DETAIL_LOG = true


    /**
     * 把日志的TAG设置为 AndroidRuntime，既可以输出正常调试日志，又可以输出异常报错日志
     */
    private const val APK_TAG = "AndroidRuntime"

    /**
     * 一般放在方法的第一行，主要用于标记方法的执行
     */
    @JvmStatic
    fun i() {
        if (!ENABLE_LOG) {
            return
        }
        var fileInfo: String? = null
        if (ENABLE_DETAIL_LOG) {
            // val stackTrace = Thread.currentThread().stackTrace[2]  // For Java
            val stackTrace = Thread.currentThread().stackTrace[3]  // For Android
            fileInfo = (stackTrace?.fileName ?: "Anonymous") + "(" + stackTrace.lineNumber + ") " + stackTrace.methodName
        }
        if (isEmpty(fileInfo)) {
            println("")
        } else {
            println(fileInfo!!)
        }
    }

    @JvmStatic
    fun i(msg: String) {
        if (!ENABLE_LOG) {
            return
        }
        var fileInfo: String? = null
        if (ENABLE_DETAIL_LOG) {
            val stackTrace = Thread.currentThread().stackTrace[3]
            fileInfo = (stackTrace?.fileName ?: "Anonymous") + "(" + stackTrace.lineNumber + ") " + stackTrace.methodName
        }
        if (isEmpty(fileInfo)) {
            println(msg)
        } else {
            println("$fileInfo:$msg")
        }
    }


    /**
     * 用于调试时，输出参数信息
     *
     * @param msg
     */
    @JvmStatic
    fun d(msg: String) {
        if (!ENABLE_LOG) {
            return
        }
        var fileInfo: String? = null
        if (ENABLE_DETAIL_LOG) {
            val stackTrace = Thread.currentThread().stackTrace[3]
            fileInfo = (stackTrace?.fileName ?: "Anonymous") + "(" + stackTrace.lineNumber + ") " + stackTrace.methodName
        }
        if (isEmpty(fileInfo)) {
            println(msg)
        } else {
            println("$fileInfo:$msg")
        }
    }

    /**
     * 用于调试时，输出报错信息
     *
     * @param msg
     */
    @JvmStatic
    fun e(msg: String) {
        if (!ENABLE_LOG) {
            return
        }
        var fileInfo: String? = null
        if (ENABLE_DETAIL_LOG) {
            val stackTrace = Thread.currentThread().stackTrace[3]
            // 如果 stackTrace 对象不为 null，则返回 stackTrace.fileName 的值；
            // 如果 stackTrace 对象为 null，则返回 "Anonymous"。
            fileInfo = (stackTrace?.fileName ?: "Anonymous") + "(" + stackTrace.lineNumber + ") " + stackTrace.methodName
        }
        if (isEmpty(fileInfo)) {
            println(msg)
        } else {
            println("$fileInfo:$msg")
        }
    }


    fun limitedLogger(message: String, maxLogs: Int) {
        val counter = AtomicInteger(0)
        if (counter.getAndIncrement() <= maxLogs) {
            println(message)
        }
    }


    private fun isEmpty(str: CharSequence?): Boolean {
        return str.isNullOrEmpty()
    }
}