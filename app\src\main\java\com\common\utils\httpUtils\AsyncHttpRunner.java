package com.common.utils.httpUtils;

import java.util.ArrayList;

/**
 * 异步回调框架类。
 * 
 * <AUTHOR>
 */
public class AsyncHttpRunner {
	/**
	 * 根据 URL 异步请求数据，并在获取到数据后通过 {@link RequestListener} 接口进行回调。
	 * 请注意：该回调函数是运行在后台线程的。 另外，在调用该方法时，成功时，会调用 {@link RequestListener#onComplete}，
	 * 
	 * @param url
	 *        服务器地址
	 * @param params
	 *        存放参数的容器
	 * @param httpMethod
	 *        "GET" or "POST"
	 * @param listener
	 *        回调对象
	 * @param encode
	 *        编码
	 * @param decode
	 *        解码
	 * @param headerList
	 *        header
	 */
	
	public static void request(final String url, final HttpParameters params, final String httpMethod, final RequestListener listener, final String encode, final String decode,
			final ArrayList<MyHeader> headerList) {
		new Thread() {
			@Override
			public void run() {
				try {
					String resp = HttpManager.openUrl(url, httpMethod, params, encode, decode, headerList);
					if (listener != null) {
						listener.onComplete(resp);
					}
				}
				catch (MyException e) {
					if (listener != null) {
						listener.onError(e);
					}
				}
			}
		}.start();
	}
}
