package com.test.android

/**
 * 计算贷款的年化利率（不太准确）
 */
fun main() {
    val loanAmount = 36 * 100      // 借款金额
    val numberOfMonths = 6          // 还款期数/月数
    // val numberOfMonths = 12      // 还款期数/月数
    val monthlyPayment = 631.34    // 每月平均还款金额

    // 使用二分法计算年化利率
    val annualRate = calculateAnnualRate(loanAmount, monthlyPayment, numberOfMonths)
    var formattedNumber = String.format("%.2f", annualRate * 100)
    println("年化利率: $formattedNumber%")
}

fun calculateAnnualRate(loanAmount: Int, monthlyPayment: Double, numberOfMonths: Int): Double {
    var lowerBound = 0.0
    var upperBound = 1.0
    val precision = 1e-6 // 精度
    while (upperBound - lowerBound > precision) {
        val midRate = (lowerBound + upperBound) / 2
        val monthlyRate = midRate / 12
        val presentValue = (0 until numberOfMonths).sumOf {
            monthlyPayment / Math.pow(1 + monthlyRate, it.toDouble())
            // monthlyPayment / (1 + monthlyRate).pow(it.toDouble())
        }
        if (presentValue < loanAmount) {
            upperBound = midRate // 需要增加利率
        } else {
            lowerBound = midRate // 需要减少利率
        }
    }
    return (lowerBound + upperBound) / 2
}
