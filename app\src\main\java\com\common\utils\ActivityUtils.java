package com.common.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @todo activity相关的工具类
 * @time 2017/4/2 18:51
 */
public class ActivityUtils {

    /**
     * 跳转到某个intent之前，先判断该intent是否有效。一般在跳转到其他APP的activity时用到。
     *
     * @param context
     * @param action
     * @return
     */
    private static boolean isIntentAvailable(Context context, String action) {
        final PackageManager packageManager = context.getPackageManager();
        final Intent intent = new Intent(action);
        List<ResolveInfo> resolveInfo = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
        if (resolveInfo.size() > 0) {
            return true;
        }
        return false;
    }
}
