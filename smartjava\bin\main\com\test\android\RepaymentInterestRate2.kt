import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.math.abs
import kotlin.math.pow

/**
* 计算贷款的年化利率
*/

// 定义一个数据类来存储每笔还款的信息
data class PaymentInfo(val date: LocalDate, val amount: Double)

/**
 * 计算给定日利率下，所有未来现金流（还款）的现值之和与贷款本金之间的差额 (NPV - Loan Amount)
 * @param dailyRate 假设的日利率
 * @param loanAmount 贷款本金（通常为负值，因为是支出，但在我们的公式中作为比较基准，所以用正值）
 * @param loanDate 贷款发放日期
 * @param payments 还款列表
 * @return Double 计算出的净现值与贷款本金的差额
 */
fun calculateNetPresentValueDifference(
    dailyRate: Double,
    loanAmount: Double,
    loanDate: LocalDate,
    payments: List<PaymentInfo>
): Double {
    var sumOfPresentValues = 0.0
    for (payment in payments) {
        val daysBetween = ChronoUnit.DAYS.between(loanDate, payment.date).toDouble()
        // PV = FV / (1 + r)^n
        // FV是未来的还款额 (payment.amount)
        // r是日利率 (dailyRate)
        // n是距离贷款发放日的天数 (daysBetween)
        sumOfPresentValues += payment.amount / (1.0 + dailyRate).pow(daysBetween)
    }
    return sumOfPresentValues - loanAmount // 我们希望这个值趋近于0
}

/**
 * 使用二分法寻找使净现值差额为零的日利率，并计算年化利率
 * @param loanAmount 贷款本金
 * @param loanDate 贷款发放日期
 * @param payments 还款列表
 * @param tolerance 允许的误差范围
 * @param maxIterations 最大迭代次数
 * @return Double 年化利率 (APR/EAR)，以百分比形式表示 (例如 15.5 表示 15.5%)
 */
fun findAnnualRate(
    loanAmount: Double,
    loanDate: LocalDate,
    payments: List<PaymentInfo>,
    tolerance: Double = 1E-9, // NPV差额的容忍度
    maxIterations: Int = 1000  // 迭代次数
): Double {
    var lowDailyRate = 0.0    // 最低可能的日利率（0%）
    var highDailyRate = 0.01  // 初始猜测一个较高的日利率上限 (1% daily rate)

    // 检查初始上限是否足够高：在highDailyRate下，NPV差额应该是负数或接近零
    // 如果是正数，说明真实利率比highDailyRate还高，需要调高上限
    var initialNpvDiffAtHighRate = calculateNetPresentValueDifference(highDailyRate, loanAmount, loanDate, payments)
    while (initialNpvDiffAtHighRate > 0 && highDailyRate < 0.1) { // 增加上限，直到NPV变为负数或达到一个非常高的利率
        highDailyRate *= 2 // 快速扩大搜索范围
        initialNpvDiffAtHighRate = calculateNetPresentValueDifference(highDailyRate, loanAmount, loanDate, payments)
        if (highDailyRate >= 0.1 && initialNpvDiffAtHighRate > 0) {
            println("Warning: Could not find a high enough rate where NPV becomes negative. Current high daily rate: $highDailyRate, NPV diff: $initialNpvDiffAtHighRate. Result might be inaccurate or the interest rate is extremely high.")
            break;
        }
    }

    // 检查在0%利率时NPV是否为负，这通常表示输入数据有问题（还款总额小于贷款本金）
    val npvAtZeroRate = calculateNetPresentValueDifference(0.0, loanAmount, loanDate, payments)
    if (npvAtZeroRate < 0) {
        println("Warning: Sum of payments (${payments.sumOf { it.amount }}) is less than loan amount ($loanAmount) even at 0% interest. Check input values. An APR cannot be meaningfully calculated in this scenario.")
        // 可以返回 NaN 或抛出异常，因为这种情况下利率无实际意义或为负（贷款语境下不常见）
        return Double.NaN // Or throw IllegalArgumentException
    }

    var dailyRate = 0.0
    for (i in 0 until maxIterations) {
        dailyRate = (lowDailyRate + highDailyRate) / 2.0
        if (dailyRate == lowDailyRate || dailyRate == highDailyRate) { // 避免浮点数精度问题导致死循环
            break
        }
        val npvDifference = calculateNetPresentValueDifference(dailyRate, loanAmount, loanDate, payments)

        if (abs(npvDifference) < tolerance) {
            break // 找到了足够精确的解
        }

        if (npvDifference > 0) {
            // 如果现值之和大于贷款本金，说明我们使用的利率太低了，需要提高利率
            lowDailyRate = dailyRate
        } else {
            // 如果现值之和小于贷款本金，说明我们使用的利率太高了，需要降低利率
            highDailyRate = dailyRate
        }
    }

    // 将日利率年化: APR = (1 + daily_rate)^365 - 1
    // 注意：这里使用365天作为一年。对于某些金融场景，可能会使用360天。
    val annualRate = ((1.0 + dailyRate).pow(365.0) - 1.0)
    return annualRate * 100 // 返回百分比
}

fun main() {
    val loanAmount = 55 * 100.00   // 借款金额
    val loanDate = LocalDate.of(2025, 5, 22)  // 借款日期
    val paymentAmount = 966.44  // 每期还款金额
    val numberOfPayments = 6  //还款期数
    val firstPaymentDate = LocalDate.of(2025, 6, 22)  //开始还款日期

    // 根据描述生成还款日期列表
    val paymentDates = mutableListOf<LocalDate>()
    var currentPaymentDate = firstPaymentDate
    for (i in 0 until numberOfPayments) {
        paymentDates.add(currentPaymentDate)
        currentPaymentDate = currentPaymentDate.plusMonths(1) // 下一个月同一天
    }

    val payments = paymentDates.map { PaymentInfo(it, paymentAmount) }

    println("贷款金额: $loanAmount 元")
    println("贷款日期: $loanDate")
    payments.forEachIndexed { index, payment ->
        println("第 ${index + 1} 次还款: 日期 ${payment.date}, 金额 ${payment.amount} 元")
    }
    println("------------------------------------")

    val apr = findAnnualRate(loanAmount, loanDate, payments)

    if (apr.isNaN()) {
        println("无法计算有效的年化利率。请检查输入数据。")
    } else {
        println("计算得出的年化利率 (APR/EAR): ${String.format("%.4f", apr)}%")

        // 可选：验证结果，用计算出的APR反推日利率，再计算NPV差额
        val calculatedDailyRateForVerification = (1.0 + apr / 100.0).pow(1.0 / 365.0) - 1.0
        val verificationNpvDiff = calculateNetPresentValueDifference(
            calculatedDailyRateForVerification,
            loanAmount,
            loanDate,
            payments
        )
        println("用计算结果验证NPV差额 (应接近0): ${String.format("%.8f", verificationNpvDiff)}")
    }
}