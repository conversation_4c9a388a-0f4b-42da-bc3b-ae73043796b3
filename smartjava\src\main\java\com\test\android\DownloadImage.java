package com.test.android;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;

public class DownloadImage {
	final static String DOMAIN = "http://photo.guomoba.net/uploadfile/";
	final static int BEGIN_YEAR = 2015;
	static int BEGIN_MONTH = 2;
	static int BEGIN_DATE = 10;// 20160124 开始需要重新获取
	static int BEGIN_FolderNum = 10;
	final static int BEGIN_IMAGE_NAME = 53;

	public static void main(String[] args) throws Exception {
		new Thread(() -> loopGetFilesFromUrl()).start();

		/*new Thread() {
			public void run() {
				getFilesFromUrl("http://pic.44rtphoto.com/uploadfile/", "2016/1101/50", 141);
			};
		}.start();
		new Thread() {
			public void run() {
				getFilesFromUrl("http://photo.guomoba.net/uploadfile/", "2016/1012/26", 1);
			};
		}.start();*/
	}

	private static void loopGetFilesFromUrl() {
		for (int year = BEGIN_YEAR; year < 2017; year++) {
			System.out.println("year:" + year);
			if (year > BEGIN_YEAR) {
				BEGIN_MONTH = 1;
			}
			for (int month = BEGIN_MONTH; month <= 12; month++) {
				System.out.println("month:" + month);
				String monthStr;
				if (month < 10) {
					monthStr = "0" + month;
				} else {
					monthStr = String.valueOf(month);
				}
				if (year > BEGIN_YEAR || month > BEGIN_MONTH) {
					BEGIN_DATE = 1;
				}
				for (int date = BEGIN_DATE; date <= 31; date++) {
					System.out.println("date:" + date);
					String dateStr;
					if (date < 10) {
						dateStr = "0" + date;
					} else {
						dateStr = String.valueOf(date);
					}
					String monthDayStr = monthStr + dateStr;

					// http://photo.guomoba.net/uploadfile/2016/1012/26/230.jpg
					if (year > BEGIN_YEAR || month > BEGIN_MONTH || date > BEGIN_DATE) {
						BEGIN_FolderNum = 1;
					}
					for (int folderNum = BEGIN_FolderNum; folderNum < 100; folderNum++) {
						getFilesFromUrl(DOMAIN, year + "/" + monthDayStr + "/" + folderNum, BEGIN_IMAGE_NAME);
					}
				}
			}
		}
	}

	/**
	 * @param domain
	 * @param folderUrl
	 * @todo 获取指定服务器目录下的所有的文件，调用方式：
	 * 
	 * getFilesFromUrl("http://www.44rt.info/html/yazhourenti/", "2016/1101");
	 */
	private static void getFilesFromUrl(String domain, String folderUrl, int begin_image_name) {
		for (int i = begin_image_name; i < 1000; i++) {
			String imageName;
			if (i < 10) {
				imageName = "0" + i;
			} else {
				imageName = i + "";
			}

			String url = domain + folderUrl + "/" + imageName + ".jpg";
			System.out.println("url:" + url);
			// String save_image_path = "F:\\myapk\\ym\\download_image";
			String save_image_path = "F:\\myapk\\ym\\" + folderUrl.replace("/", "\\");
			System.out.println("save_image_path:" + save_image_path);
			boolean downloadResult = download(url, save_image_path);
			System.out.println("downloadResult:" + downloadResult);
			if (!downloadResult) {
				break;
			}
		}
	}

	public static boolean download(String urlString, String savePath) {
		if (urlString == null) {
			return false;
		}

		String[] split = urlString.split("/");
		String imageName = split[split.length - 1];
		String imagePath = savePath + "\\" + imageName;
		File file = new File(imagePath);
		if (file.exists()) {
			System.out.println("file always exist!");
			return true;
		}
		OutputStream os = null;
		InputStream is = null;
		try {
			URL url = new URL(urlString);
			URLConnection con = url.openConnection();
			//request timeout 5s
			con.setConnectTimeout(5 * 1000);
			is = con.getInputStream();
			//the size of data buffer area.
			byte[] bs = new byte[1024];
			// the length of read data.
			int len;
			// output file stream.
			File sf = new File(savePath);
			if (!sf.exists()) {
				sf.mkdirs();
			}

			os = new FileOutputStream(imagePath);
			// start to read data.
			while ((len = is.read(bs)) != -1) {
				os.write(bs, 0, len);
			}
		} catch (Exception e) {
			return false;
		} finally {
			// finish.close all stream.
			try {
				if (os != null) {
					os.close();
				}
				if (is != null) {
					is.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
				return false;
			}
		}
		return true;
	}

}
