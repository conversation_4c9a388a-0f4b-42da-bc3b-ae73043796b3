package com.common.utils;


import android.text.TextUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class StringUtils {
    /**
     * @param str
     * @return
     * @todo 判断是否包含中文字符
     */
    public static boolean isContainChinese(String str) {
        String regEx = "[\\u4e00-\\u9fbf]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param charaString
     * @return
     * @todo 判断是否全部为英文
     */
    public static boolean isAllEnglish(String charaString) {
        return charaString.matches("^[a-zA-Z]*");
    }

    public static boolean checkStringContainChinese(String checkStr) {
        if (!TextUtils.isEmpty(checkStr)) {
            char[] checkChars = checkStr.toCharArray();
            for (int i = 0; i < checkChars.length; i++) {
                char checkChar = checkChars[i];
                if (checkCharContainChinese(checkChar)) {
                    return true;
                }
            }
        }
        return false;
    }

    private static boolean checkCharContainChinese(char checkChar) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(checkChar);
        if (Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS == ub || Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS == ub ||
                Character.UnicodeBlock.CJK_COMPATIBILITY_FORMS == ub ||
                Character.UnicodeBlock.CJK_RADICALS_SUPPLEMENT == ub || Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A ==
                ub || Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B ==
                ub) {
            return true;
        }
        return false;
    }
}
