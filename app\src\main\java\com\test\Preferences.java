package com.test;

import android.content.Context;
import android.content.SharedPreferences;

public class Preferences {
	public static SharedPreferences mPreference;

	public static long getIntValue(String name) {
		return mPreference.getInt(name, 0);
	}

	public static long getLongValue(String name) {
		return mPreference.getLong(name, 0);
	}

	public static float getFloatValue(String name) {
		return mPreference.getFloat(name, 0f);
	}

	public static String getStringValue(String name) {
		return mPreference.getString(name, "");
	}

	public static void put(Context context, String name, Object value) {
		if (mPreference == null) {
			mPreference = context.getSharedPreferences("device_info", Context.MODE_PRIVATE);
		}
		SharedPreferences.Editor editor = mPreference.edit();
		if (value.getClass() == Boolean.class) {
			editor.putBoolean(name, (Boolean) value);
		}
		if (value.getClass() == String.class) {
			editor.putString(name, (String) value);
		}
		if (value.getClass() == Integer.class) {
			editor.putInt(name, ((Integer) value).intValue());
		}
		if (value.getClass() == Float.class) {
			editor.putFloat(name, ((Float) value).intValue());
		}
		if (value.getClass() == Long.class) {
			editor.putLong(name, ((Long) value).intValue());
		}
		editor.commit();
	}
}