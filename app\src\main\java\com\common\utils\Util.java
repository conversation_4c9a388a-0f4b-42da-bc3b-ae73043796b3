package com.common.utils;

import android.app.Activity;
import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.net.ConnectivityManager;
import android.net.NetworkInfo.State;
import android.util.DisplayMetrics;
import android.util.Log;

import com.test.Constant;
import com.test.android.R;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Util {
    public Context mContext;

    /**
     * @param file
     * @return
     * @throws FileNotFoundException
     * <AUTHOR>
     * @todo 获取文件的md5值
     * @date 2014年6月10日 下午5:09:12
     */
    public String getMd5ByFile(File file) throws FileNotFoundException {
        String value = null;
        FileInputStream in = new FileInputStream(file);
        try {
            MappedByteBuffer byteBuffer = in.getChannel().map(FileChannel.MapMode.READ_ONLY, 0, file.length());
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(byteBuffer);
            BigInteger bi = new BigInteger(1, md5.digest());
            value = bi.toString(16);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return value;
    }

    /**
     * <AUTHOR>
     * @todo 根据Unicode编码完美的判断中文汉字和符号
     * @date 2014年5月29日 下午2:34:12
     */
    private static boolean isChineseChar(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS || ub == Character.UnicodeBlock
                .CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION ||
                ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION) {
            return true;
        }
        return false;
    }

    /**
     * @todo
     * <AUTHOR>
     * @time 2017/3/30 7:25
     */
    public static String convertStreamToString(InputStream is) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int i = is.read();
        while (i != -1) {
            baos.write(i);
            i = is.read();
        }
        return baos.toString();
    }

    private static boolean checkIsExistsDataBase() {
        SQLiteDatabase checkDB = null;
        try {
            String databaseFilename = Constant.DATABASE_PATH + Constant.ROUTE_DB_NAME;
            checkDB = SQLiteDatabase.openDatabase(databaseFilename, null, SQLiteDatabase.OPEN_READWRITE);
        } catch (SQLiteException e) {

        }
        if (checkDB != null) {
            checkDB.close();
        }
        return checkDB != null ? true : false;
    }

    public static void checkCopyDbFile(Context context, String string, int route) {
        boolean dbExist = checkIsExistsDataBase();
        if (!dbExist) {
            try {
                copyDataBase(context);
            } catch (IOException e) {
                throw new Error("Error copying database");
            }
        }
    }

    private static void copyDataBase(Context context) throws IOException {
        String databaseFilenames = Constant.DATABASE_PATH + Constant.ROUTE_DB_NAME;
        File dir = new File(Constant.DATABASE_PATH);
        // 判断文件夹是否存在，不存在就新建一个
        if (!dir.exists()) {
            dir.mkdir();
        }
        FileOutputStream outPutStream = null;
        try {
            // 得到数据库文件的写入流
            outPutStream = new FileOutputStream(databaseFilenames);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        // 得到数据库文件的数据流
        InputStream inputStream = context.getResources().openRawResource(R.raw.route);
        byte[] buffer = new byte[8192];
        int count = 0;
        try {
            while ((count = inputStream.read(buffer)) > 0) {
                outPutStream.write(buffer, 0, count);
                outPutStream.flush();
            }
        } catch (IOException e) {
        }
        try {
            inputStream.close();
            outPutStream.close();
            Log.i("log_test", "数据库文件复制完毕");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * java去除字符串中的空格、回车、换行符、制表符
     *
     * @param before
     * @return
     */
    public String replaceSymbol(String before) {
        Pattern compile = Pattern.compile("\\s*|\t|\r|\n");
        Matcher matcher = compile.matcher(before);
        return matcher.replaceAll("");
    }

    /**
     * 检测手机是否连接WiFi网络（不能判断是否可以访问外网）
     *
     * @param context
     * @return TRUE：已经连接，FALSE：未连接
     */
    public boolean isWiFiAble(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        State wifi = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI).getState();
        if (wifi == State.CONNECTED) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 从网络上获取图片
     *
     * @param fileSavePath
     * @param netResourceUrl
     * @throws Exception
     */
    public void getImageFromNet(String fileSavePath, String netResourceUrl) throws Exception {
        File file = new File(fileSavePath);
        File parentFile = file.getParentFile();
        if (!parentFile.exists()) {
            parentFile.mkdir();
        }
        // 从网络上获取图片
        URL url = new URL(netResourceUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(5000);
        conn.setRequestMethod("GET");
        conn.setDoInput(true);
        if (conn.getResponseCode() == 200) {
            InputStream is = conn.getInputStream();
            FileOutputStream fos = new FileOutputStream(file);
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = is.read(buffer)) != -1) {
                fos.write(buffer, 0, len);
            }
            is.close();
            fos.flush();
            fos.close();
        }
    }

    /**
     * 获取屏幕的高
     *
     * @param activity
     * @return
     */
    public int getScreenHeigth(Activity activity) {
        DisplayMetrics metrics = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(metrics);
        int screenWidth = metrics.widthPixels;
        int screenHeight = metrics.heightPixels;

        if (screenWidth > screenHeight) {
            return screenWidth;
        } else {
            return screenHeight;
        }
    }

    /**
     * 获取屏幕的宽
     *
     * @param activity
     * @return
     */
    public int getScreenWidth(Activity activity) {
        DisplayMetrics metrics = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(metrics);
        int screenWidth = metrics.widthPixels;
        int screenHeight = metrics.heightPixels;

        if (screenWidth > screenHeight) {
            return screenHeight;
        } else {
            return screenWidth;
        }
    }


    /**
     * @todo
     * <AUTHOR>
     * @time 2017/3/30 7:27
     */
    public int getScreenDensity(Activity activity) {
        DisplayMetrics metrics = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(metrics);
        return metrics.densityDpi;
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public int dip2px(Context context, float dpValue) {
        if (context == null) {
            return 0;
        }
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }

    /**
     * 根据手机的分辨率从 px(像素) 的单位 转成为 dp
     */
    public int px2dip(Context context, float pxValue) {
        if (context == null) {
            return 0;
        }
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }

    /**
     * 将px值转换为sp值，保证文字大小不变
     *
     * @param pxValue
     * @param fontScale
     * @return
     */
    public int px2sp(Context context, float pxValue) {
        if (context == null) {
            return 0;
        }
        final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
        return (int) (pxValue / fontScale + 0.5f);
    }

    /**
     * 将sp值转换为px值，保证文字大小不变
     *
     * @param spValue
     * @param fontScale
     * @return
     */
    public int sp2px(Context context, float spValue) {
        if (context == null) {
            return 0;
        }
        final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
        return (int) (spValue * fontScale + 0.5f);
    }

    /**
     * 计算流量显示
     *
     * @param flowcount 单位为B
     * @return
     */
    public String computerFlow(long flowcount) {
        double flowamount = 1.0;
        flowcount = flowcount / 1024;// 转换单位为KB
        long tempflowcount = flowcount;
        if (flowcount < 0) {
            return ("0K");
        }
        if (flowcount < 1024) {
            return (flowcount + "K");
        }
        int MB = 1048576;
        if (tempflowcount < MB)// <1G
        {
            flowamount = flowamount * flowcount / 1024;// 转换单位为MB
            flowamount = (double) (Math.round(flowamount * 1000) / 1000.0);
            return (String.format("%.2f", flowamount) + "M");// 四舍五入保留2位小数
        }
        // 大于1G
        tempflowcount = flowcount / 1024;// 转换单位为MB
        flowamount = flowamount * tempflowcount / 1024;// 转换单位为GB
        flowamount = (double) (Math.round(flowamount * 1000) / 1000.0);
        return (String.format("%.2f", flowamount) + "G");// 四舍五入保留2位小数
    }

    /**
     * @todo
     * <AUTHOR>
     * @time 2017/3/30 7:29
     */
    public int getRandom(int high, int low) {
        return (int) (Math.random() * (high - low) + low);
    }

}
