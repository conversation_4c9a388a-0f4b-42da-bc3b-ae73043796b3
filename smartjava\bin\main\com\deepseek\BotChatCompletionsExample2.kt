package com.deepseek

import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionRequest
import com.volcengine.ark.runtime.model.bot.completion.chat.reference.BotChatResultReference
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChoice
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole
import com.volcengine.ark.runtime.service.ArkService
import java.util.ArrayList
import java.util.function.Consumer

object BotChatCompletionsExample2 {
    @JvmStatic
    fun main(args: Array<String>) {
        val apiKey = System.getenv("ARK_API_KEY")
        val arkService = ArkService.builder().apiKey(apiKey).build()

        println("\n----- standard request -----")
        val chatMessages: MutableList<ChatMessage?> = ArrayList<ChatMessage?>()
        val systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build()
        val userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("常见的十字花科植物有哪些？").build()
        chatMessages.add(systemMessage)
        chatMessages.add(userMessage)

        val chatCompletionRequest = BotChatCompletionRequest.builder()
            .model("ep-20250216011843-rsfsn").messages(chatMessages).build()

        val chatCompletionResult = arkService.createBotChatCompletion(chatCompletionRequest)
        chatCompletionResult.getChoices().forEach(Consumer { choice: ChatCompletionChoice? -> println(choice!!.getMessage().getContent()) })
        // the references example
        chatCompletionResult.getReferences().forEach(Consumer { ref: BotChatResultReference? -> println(ref!!.getUrl()) })
    }
}