package com.test.android;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;

/**
 *@通过了解网站的图片的存储的路径的规则，批量的自动化下载网站的图片
 */
public class DownloadImageTest {
	/**
	 * @param args
	 * @throws Exception
	 */
	public static void main(String[] args) throws Exception {
		for (int year = 2016; year < 2017; year++) {
			for (int month = 1; month <= 12; month++) {
				String monthStr;
				if (month < 10) {
					monthStr = "0" + month;
				} else {
					monthStr = String.valueOf(month);
				}
				for (int date = 15; date <= 31; date++) {
					String dateStr;
					if (date < 10) {
						dateStr = "0" + date;
					} else {
						dateStr = String.valueOf(date);
					}
					String monthDayStr = monthStr + dateStr;
					/*if (monthDay < 10) {
						continue;
					} else if (monthDay < 100) {
						continue;
					} else if (monthDay < 1000) {
						monthDayStr = "0" + monthDay;
					} else {
						monthDayStr = monthDay + "";
					}*/
					/*year=2016;
					monthDayStr="1012";*/
					// http://photo.guomoba.net/uploadfile/2016/1012/26/230.jpg
					for (int folderNum = 0; folderNum < 100; folderNum++) {
						for (int i = 1; i < 1000; i++) {
							String imageName;
							if (i < 10) {
								imageName = "0" + i;
							} else {
								imageName = i + "";
							}
							String url = "http://photo.guomoba.net/uploadfile/" + year + "/" + monthDayStr + "/" + folderNum + "/" + imageName + ".jpg";
							System.out.println("url:" + url);
							// String save_image_path
							// ="F:\\myapk\\ym\\download_image";
							String save_image_path = "F:\\myapk\\ym\\" + year + "\\" + monthDayStr + "\\" + folderNum;
							System.out.println("save_image_path:" + save_image_path);
							boolean downloadResult = download(url, save_image_path);
							System.out.println("downloadResult:" + downloadResult);
							if (!downloadResult) {
								break;
							}
						}
					}
				}
			}
		}
	}

	public static boolean download(String urlString, String savePath) {
		if (urlString == null) {
			return false;
		}
		OutputStream os = null;
		InputStream is = null;
		try {
			URL url = new URL(urlString);
			URLConnection con = url.openConnection();
			con.setConnectTimeout(5 * 1000);
			is = con.getInputStream();
			byte[] bs = new byte[1024];
			int len;
			File sf = new File(savePath);
			if (!sf.exists()) {
				sf.mkdirs();
			}
			String[] split = urlString.split("/");
			String imageName = split[split.length - 1];
			os = new FileOutputStream(sf.getPath() + "\\" + imageName);
			while ((len = is.read(bs)) != -1) {
				os.write(bs, 0, len);
			}
		} catch (Exception e) {
			return false;
		} finally {
			try {
				if (os != null) {
					os.close();
				}
				if (is != null) {
					is.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
				return false;
			}
		}
		return true;
	}

}
