package com.test.android.file;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

public class MyFileInputStream extends FileInputStream {

    public MyFileInputStream(String name) throws FileNotFoundException {
        super(name);
    }

    @Override
    public int read(byte[] b) throws IOException {
        int getData = read();
        if (getData==-1) {
            return -1;
        }else{
            b[0] = (byte)getData;
            for (int i = 1; i < b.length; i++) {
                getData = read();
                if(-1==getData)
                    return i;
                b[i] = (byte)getData;
            }
        }
        return b.length;
    }
}