//局限性：正在演示的时候，请勿调整窗口的大小，否则会出现 方块不对齐的情况；
//但是，演示之前 可以随意的调整窗口的大小

package com.test.android.ui;

import java.awt.BorderLayout;
import java.awt.GridLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.JPanel;

class display extends J<PERSON>rame implements Runnable, ActionListener {//继承JFrame，Runnable, ActionListener
    private static final long serialVersionUID = 1L;
    final JFrame frame = new JFrame("拼图游戏演示程序");
    final JPanel numberPanel = new JPanel();//初始化面板
    final JPanel btnPanel = new JPanel();
    final JButton[] jb = new JButton[8];//初始化8个按钮
    static int x5;
    static int x2;
    static int y6;
    static int y2;
    static int width;
    static int height;
    Thread starThread;//声明一个继承
    static final int[] num = {4, 1, 3, 7, 2, 5, 8, 6};

    public display() {
        GridLayout grid = new GridLayout(3, 3);//网格布局
        JButton startButton = new JButton("开始演示");
        starThread = new Thread(this);//初始化进程
        btnPanel.add(startButton);        //把startButton按钮加入面板中
        for (int i = 0; i < 8; i++) {//创建按钮，并显示名称，然后 在面板中添加按钮
            jb[i] = new JButton(num[i] + "");
            numberPanel.add(jb[i]);
        }
        numberPanel.setLayout(grid); //面板为网格布局
        frame.add(numberPanel, BorderLayout.CENTER);//面板放在窗口BorderLayout布局的中部
        frame.add(btnPanel, BorderLayout.NORTH);//面板放在窗口BorderLayout布局的北部
        frame.setBounds(380, 120, 600, 600);//设置窗口的位置与大小
        frame.setVisible(true);
        frame.validate();
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        addWindowListener(new WindowAdapter() {//窗口坚挺事件
            public void windowClosing(WindowEvent e) {
                System.exit(0);
            }
        });
        startButton.addActionListener(this);//注册startButton的监听事件

    } // display()

    public void actionPerformed(ActionEvent e) {
        //startButton的响应事件
        if (!starThread.isAlive()) {   //决定是否创建进程
            starThread = new Thread(this);
        }
        try {
            starThread.start(); //启动进程
        } catch (Exception exp) { //捕获异常

        }
    }

    public void run() {//重写run方法
        if (Thread.currentThread() == starThread) { //判断当前运行的进程是否为startThread进程
            for (int j = 0; j < 8; j++) {//获取当前按钮的横纵坐标信息
                x2 = jb[4].getX();
                y2 = jb[4].getY();
                x5 = jb[5].getX();
                y6 = jb[7].getY();
            }
            threadsleep(1500);//暂停1.5秒
            jb[7].setLocation(x5, y6);//把第七个按钮移动到(x5, y6)这个坐标位置
            threadsleep(2000);//暂停2秒
            jb[6].setLocation(x2, y6);//把第六个按钮移动到(x2, y6)这个坐标位置
            threadsleep(2000);
            jb[3].setLocation(0, y6);
            threadsleep(2000);
            jb[0].setLocation(0, y2);
            threadsleep(2000);
            jb[1].setLocation(0, 0);
            threadsleep(2000);
            jb[4].setLocation(x2, 0);
            threadsleep(2000);
            jb[5].setLocation(x2, y2);
            threadsleep(2000);
            jb[7].setLocation(x5, y2);
            threadsleep(58);//暂停58毫秒
            JOptionPane.showMessageDialog(frame, "见春颖！恭喜你，闯关成功！");    //弹出提示窗口
        }

    }  // run()

    void threadsleep(int time) { //进程暂停的 调用函数
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }
} // class display

public class PinTuDisplay {
    public static void main(String[] args) {
        new display();//调用构造函数
    }
}