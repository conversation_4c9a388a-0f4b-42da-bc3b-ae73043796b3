<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.test.activity.MainActivity">

    <TextView
        android:id="@+id/txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Hello World!"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.051"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.031"/>

    <Button
        android:id="@+id/btn_thread"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:text="ThreadTest"
        app:layout_constraintLeft_toRightOf="@+id/txt"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="118dp"
        tools:layout_editor_absoluteY="5dp"/>

    <Button
        android:id="@+id/btn_play_sound"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:text="PlaySound"
        app:layout_constraintLeft_toRightOf="@+id/btn_thread"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="118dp"
        tools:layout_editor_absoluteY="5dp"/>

    <Button
        android:id="@+id/btn_copy_database"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="CopyDatabase"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txt"
        tools:layout_editor_absoluteX="118dp"
        tools:layout_editor_absoluteY="5dp"/>

    <Button
        android:id="@+id/btn_start_voice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="startVoice"
        app:layout_constraintLeft_toRightOf="@id/btn_copy_database"
        app:layout_constraintTop_toBottomOf="@id/btn_thread"
        tools:layout_editor_absoluteX="118dp"
        tools:layout_editor_absoluteY="5dp"/>

    <Button
        android:id="@+id/btn_memory_leak"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="MemoryLeak"
        app:layout_constraintLeft_toRightOf="@id/btn_start_voice"
        app:layout_constraintTop_toBottomOf="@id/btn_play_sound"
        tools:layout_editor_absoluteX="248dp"
        tools:layout_editor_absoluteY="48dp"/>

    <Button
        android:id="@+id/btn_ClipBook"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="ClipBook"
        app:layout_constraintLeft_toLeftOf="@id/btn_copy_database"
        app:layout_constraintTop_toBottomOf="@id/btn_copy_database"
        tools:layout_editor_absoluteX="16dp"
        tools:layout_editor_absoluteY="96dp"/>

    <Button
        android:id="@+id/btn_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="animation"
        app:layout_constraintLeft_toRightOf="@id/btn_ClipBook"
        app:layout_constraintTop_toBottomOf="@id/btn_copy_database"
        tools:layout_editor_absoluteX="94dp"
        tools:layout_editor_absoluteY="104dp"/>

    <ImageView
        android:id="@+id/iv_fill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/anim_heart_filling"
        app:layout_constraintLeft_toRightOf="@id/iv_empty"
        app:layout_constraintTop_toTopOf="@id/iv_empty"
        tools:layout_editor_absoluteX="197dp"
        tools:layout_editor_absoluteY="104dp"
        tools:src="@tools:sample/avatars"/>

    <ImageView
        android:id="@+id/iv_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/anim_heart_emptying"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_animation"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="152dp"
        tools:src="@tools:sample/avatars[1]"/>

    <ImageView
        android:id="@+id/iv_anim_selector"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_empty"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="280dp"
        tools:src="@tools:sample/avatars[2]"/>

    <Button
        android:id="@+id/btn_property_anim"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="PropertyAnim"
        app:layout_constraintLeft_toRightOf="@id/iv_anim_selector"
        app:layout_constraintTop_toTopOf="@id/iv_anim_selector"
        tools:layout_editor_absoluteX="128dp"
        tools:layout_editor_absoluteY="280dp"/>

    <Button
        android:id="@+id/btn_property_anim2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="property_anim2"
        app:layout_constraintLeft_toRightOf="@id/iv_anim_selector"
        app:layout_constraintTop_toBottomOf="@id/btn_property_anim"
        tools:layout_editor_absoluteX="128dp"
        tools:layout_editor_absoluteY="328dp"/>

    <Button
        android:id="@+id/btn_property_anim3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="property_anim3"
        app:layout_constraintLeft_toRightOf="@id/iv_anim_selector"
        app:layout_constraintTop_toBottomOf="@id/btn_property_anim2"
        tools:layout_editor_absoluteX="128dp"
        tools:layout_editor_absoluteY="328dp"/>

    <Button
        android:id="@+id/btn_android_js"
        android:layout_width="128dp"
        android:layout_height="51dp"
        android:onClick="onClick_android_js"
        android:text="android_js"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_anim_selector"
        tools:layout_editor_absoluteX="-2dp"
        tools:layout_editor_absoluteY="409dp"/>
</android.support.constraint.ConstraintLayout>