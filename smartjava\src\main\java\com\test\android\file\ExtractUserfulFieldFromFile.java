package com.test.android.file;


import com.test.android.DownloadImageTest;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;

/**
 * ::脚本CalligraphyVideo.bat操作符氏书画教育APP，配合VNET抓包;
 * ::然后，通过E:\develop\Studio_WorkPlace\TestAndroid\smartjava\src\main\java\com\test\android\file\ExtractUserfulFieldFromFile.java分析并解析抓包文件中的视频链接，并下载视频到指定的目录;
 */
public class ExtractUserfulFieldFromFile {
    private static String video_path_txt_folder = "H:\\AutoSmart\\jq\\common_test_script\\adb_file\\export";

    public static void main(String[] args) {
        File videoPathFolder = new File(video_path_txt_folder);
        File[] files = videoPathFolder.listFiles();
        FileReader fileReader = null;
        char[] temp;
        for (File item : files) {
            temp = new char[5000];
            try {
                fileReader = new FileReader(item);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
            try {
                fileReader.read(temp);
            } catch (IOException e) {
                e.printStackTrace();
            }
            String str = String.valueOf(temp);
            //System.out.println(str);
            //int startIndex = str.indexOf("https://fssh-video.oss-cn-shenzhen");
            int startIndex = str.indexOf("http://vod.fushiwenhua.com");
            if (startIndex == -1) {
                continue;
            }
            int endIndex = str.indexOf(".mp4");
            if (endIndex == -1) {
                continue;
            }
            String videoUrl = str.substring(startIndex, endIndex + ".mp4".length());
            System.out.println(videoUrl);

            int name_start_index = videoUrl.lastIndexOf("/");
            String videoFileName = videoUrl.substring(name_start_index + 1);
            String save_image_path = "D:\\LeanLesson\\硬笔\\硬笔行书\\05_12岁-18岁_篇章书写技巧_提升文章书写美观";
            if (new File(save_image_path + "\\" + videoFileName).exists()) {
                System.out.println("video already exist");
                continue;
            }
            boolean downloadResult = DownloadImageTest.download(videoUrl, save_image_path);
            System.out.println("downloadResult:" + downloadResult);
        }
        try {
            fileReader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
