package com.test.android.common.thread;


import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


        ///how to use
        /*private void startHeartbeatTimer() {
            HeartbeatTimerScheduled heartbeatTimer = new HeartbeatTimerScheduled();
            heartbeatTimer.setOnScheduleListener(new HeartbeatTimerScheduled.OnScheduleListener() {
                @Override
                public void onSchedule() {
                //do something
                }
            });
            heartbeatTimer.startTimer(0, 1000 * 10);
        }*/

        /*if (heartbeatTimer != null) {
                heartbeatTimer.exit();
         }*/

/**
 * <AUTHOR>
 * @todo always do something interval some time.
 * @time 2017/10/18 18:10
 */
public class HeartbeatTimerScheduled {
    private ScheduledExecutorService scheduledExecutorService;
    private TimerTask timerTask;
    private OnScheduleListener mListener;


    public HeartbeatTimerScheduled() {
        //timer = new Timer();
        //详见文章：Java 并发专题 ： Timer的缺陷 用ScheduledExecutorService替代.mhtml

        ///scheduledExecutorService = Executors.newScheduledThreadPool(2);
        //线程池不允许使用Executors创建
        //采用ScheduledThreadPoolExecutor的方式创建，可以让代码更容易理解，以及避免资源耗尽的风险;
        //commons-lang3-3.5.jar for BasicThreadFactory
        scheduledExecutorService = new ScheduledThreadPoolExecutor(1, new BasicThreadFactory.Builder().namingPattern("example-schedule-pool-%d").daemon(true).build());
    }

    public void startTimer(long delay, long period) {
        timerTask = new TimerTask() {
            @Override
            public void run() {
                if (mListener != null) {
                    mListener.onSchedule();
                }
            }
        };
        ///timer.schedule(timerTask, delay, period);
        scheduledExecutorService.scheduleAtFixedRate(timerTask, delay, period, TimeUnit.MILLISECONDS);
    }

    public void exit() {
        if (scheduledExecutorService != null && !scheduledExecutorService.isShutdown()) {
            scheduledExecutorService.shutdown();
        }
        if (timerTask != null) {
            timerTask.cancel();
        }
    }


    public interface OnScheduleListener {
        /**
         * do something
         */
        void onSchedule();
    }

    public void setOnScheduleListener(OnScheduleListener listener) {
        this.mListener = listener;
    }
}
