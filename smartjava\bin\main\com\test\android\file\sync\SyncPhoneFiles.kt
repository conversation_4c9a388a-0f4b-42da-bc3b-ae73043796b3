package com.test.android.file.sync

fun main() {
    doCopyDirStruc()
    doMoveDirStrucFiles()
    doRenamePhoneFileAI()
    doMovePhoneFiles()
}

fun getUserName(): String {
    // val userID = 0  // Bingo
    // val userID = 1  // Hua
    val userID = 2  // Xiu
    // val userID = 3  // Lover
    // val userID = 4  // Tao
    // val userID = 5  // Ying
    // val userID = 6  // Zhen
    return getAllUserNames()[userID]
    // return ""     //单独运行RenamePhoneFileAI.kt，且不需要添加用户名后缀的时候，需要开启此项;
}


fun getSourcePath(): String {
    return "F:\\Temp\\Download\\TempDC"
    // return "H:\\not_type"
    // return "H:\\Lover\\2025"
    // return "E:\\jxbycg"
    // return "H:\\Family\\Ying\\PhoneMediaSync"
    // return "H:\\Family\\Ying\\PhoneMediaSync\\File"
    // return "H:\\Family\\Ying\\PhoneMediaSync\\sdcard\\Pictures\\.thumbnails"
    // return "H:\\Family\\Phone"
    // return "H:\\Family\\小小王\\2025"
}

fun getAllUserNames(): Array<String> {
    //                0       1      2       3       4      5       6
    return arrayOf("Bingo", "Hua", "Xiu", "Lover", "Tao", "Ying", "Zhen")
}


fun getTargetPath(): String {
    if (getUserName() == "Lover") {
        return "H:\\Family\\Lover"
    } else if (getUserName() == "Bingo") {
        return "H:\\Family\\Bingo"
    }
    return "H:\\Family\\Phone"  // Hua Xiu Tao Ying Zhen
}


fun isRenamePhoneFileTestMode(): Boolean {
    return false
    // return true
}
