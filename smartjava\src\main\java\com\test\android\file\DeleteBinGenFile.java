package com.test.android.file;

import com.test.android.SomeUtils;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.Collection;

/**
 * Created by Administrator on 2018/2/20.
 */
public class DeleteBinGenFile {

    /**
     * 不建议这样删除，因为bin、build中的apk文件，有时还是需要的。
     *
     * @param args
     * @throws Exception
     */
    public static void main(String args[]) throws Exception {
        String dir = "E:\\develop\\smart_workplace";
        Collection<File> files = (Collection<File>) FileUtils.listFiles(new File(dir), null, true);
        for (File file : files) {
            if (file.getName().equalsIgnoreCase(".project") || file.getName().equalsIgnoreCase("gradle.properties")) {
                String parentPath = file.getParent();
                //String binPath = parentPath + "\\bin";
                String genPath = parentPath + "\\gen";
                //FileUtils.deleteDirectory(new File(binPath));
                FileUtils.deleteDirectory(new File(genPath));


                String buildPath = parentPath + "\\build";
                FileUtils.deleteDirectory(new File(buildPath));
            }
        }
    }

    public static boolean deleteAllFile(File dir, String fileExcept) {
        if (!dir.exists()) {
            return true;
        }
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteAllFile(new File(dir, children[i]), fileExcept);
                if (!success) {
                    return false;
                }
            }
            return true;
        } else {
            if (SomeUtils.isTextEmpty(fileExcept) || !dir.getName().contains(fileExcept)) {
                return dir.delete();
            }
            return true;
        }
    }

}
