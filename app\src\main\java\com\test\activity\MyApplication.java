package com.test.activity;

import android.app.Application;

import com.common.utils.DeviceAppUtils;
import com.common.utils.InitWrapper;
import com.squareup.leakcanary.LeakCanary;

public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();

        if (LeakCanary.isInAnalyzerProcess(this)) {
            // This process is dedicated to LeakCanary for heap analysis.
            // You should not init your app in this process.
            return;
        }
        /*mRefWatcher = */
        LeakCanary.install(this);
        InitWrapper.init(getApplicationContext());
        DeviceAppUtils.logAPPVersionInfo(getApplicationContext());
    }

    //private RefWatcher mRefWatcher;

    /**
     * 运用于检测fregment的内存泄漏
     *
     * 在fregment类的，onDestroy()方法中，
     *  RefWatcher refWatcher = ExampleApplication.getRefWatcher(getActivity());
     *  refWatcher.watch(this);
     *
     * @param context
     * @return
     */
    /*public static RefWatcher getRefWatcher(Context context) {
        MyApplication application = (MyApplication) context.getApplicationContext();
        return application.mRefWatcher;
    }*/
}
