package com.test.memoryleak;

import android.app.Activity;
import android.os.Bundle;

import com.squareup.leakcanary.RefWatcher;
import com.test.activity.MyApplication;

public class MemoryLeakActivity5 extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onResume() {
        super.onResume();
        LoadPicThread loadPicThread = new LoadPicThread();
        loadPicThread.start();
    }

    private class LoadPicThread extends Thread {
        @Override
        public void run() {
            super.run();
            while (true) {
                //…load data…
                try {
                    Thread.sleep(1000 * 60 * 5);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        /*MyApplication.getRefWatcher(this).watch(this);*/
    }
}
