package com.test.android;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.PrintStream;

public class SomeUtils {
    /**
     * @param singleNumber
     * @return
     * @todo 在小于10的数字前面，加上0
     */
    public static String convertSingleNumber(int singleNumber) {
        String doubleString;
        if (singleNumber < 10) {
            doubleString = "0" + singleNumber;
        } else {
            doubleString = String.valueOf(singleNumber);
        }
        return doubleString;
    }

    public static boolean isTextEmpty(String text) {
        if (text == null || text.length() == 0) {
            return true;
        }
        return false;
    }

    /**
     * @param textLogFilePath
     * @todo 设置System.setOut(printStream)之后，System.out.println输出的日志信息，就会保存到指定的文本文件中,而不输出到console控制台中。
     * <AUTHOR>
     * @date 2017年1月8日 下午1:27:31
     */
    public static void printLogToFile(String textLogFilePath) {
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(textLogFilePath);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        PrintStream printStream = new PrintStream(outputStream);
        if (printStream != null) {
            System.setOut(printStream);
        }
    }
}
