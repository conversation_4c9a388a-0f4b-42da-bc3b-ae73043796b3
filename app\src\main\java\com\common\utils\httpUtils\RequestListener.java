package com.common.utils.httpUtils;

import java.io.IOException;

/**
 * 发起访问请求时所需的回调接口。
 */
public interface RequestListener {
	
	/**
	 * 当获取服务器返回的字符串后，该函数被调用。
	 * 
	 * @param response
	 *        服务器返回的字符串
	 */
	public void onComplete(String response);
	
	/**
	 * 当访问服务器时，发生 I/O 异常时，该函数被调用。
	 * 
	 * @param e
	 *        I/O 异常对象
	 */
	public void onIOException(IOException e);
	
	/**
	 * 当访问服务器时，其它异常时，该函数被调用。
	 * 
	 * @param e
	 *        http自定义异常对象
	 */
	public void onError(MyException e);
}
