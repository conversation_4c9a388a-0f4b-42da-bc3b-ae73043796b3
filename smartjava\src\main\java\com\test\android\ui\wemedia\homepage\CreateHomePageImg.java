package com.test.android.ui.wemedia.homepage;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.imageio.ImageIO;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;

/**
 * 2020-0216开发了第一个版本；
 * <p>
 * 后续功能升级：1.判断HomePage_1920_1080.jpg\Vertical_BG_1920_3415.jpg是否存在、尺寸是否满足要求。
 */
public class CreateHomePageImg {
    /**
     * @param args
     * @todo TODO
     * <AUTHOR>
     * @date 2020-2-14 下午1:34:08
     */
    public static void main(String[] args) {
        new CreateHomePageImgUI();
    }
}

class CreateHomePageImgUI extends JFrame implements Runnable, ActionListener {
    private int FRAME_LEFT = 380, FRAME_TOP = 120, FRAME_RIGHT = 366, FRAME_BOTTOM = 135;
    private final String HomePage_1920_1080 = "HomePage_1920_1080.jpg",//source images
            Vertical_BG_1920_3415 = "Vertical_BG_1920_3415.jpg",
            HomePage_Bilibili_1146_717 = "HomePage_Bilibili_1146_717.jpg",//gen images
            HomePage_Sohu_1075_717 = "HomePage_Sohu_1075_717.jpg",
            HomePage_1920_3415_Vertical = "HomePage_1920_3415_Vertical.jpg",
            HomePage_1080_1920_Vertical = "HomePage_1080_1920_Vertical.jpg",
            RELEASE_COMPONENT_FOLDER = "H:\\AutoSmart\\jq\\DoWithSmart\\WeMedia\\TouTiao\\Image\\Release";//folder


    private JLabel mJLabel;

    private Thread startThread;// 声明一个线程

    public CreateHomePageImgUI() {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyHHdd");
		String format = simpleDateFormat.format(new Date());
		JFrame jFrame = new JFrame("CreateWeMediaHomePageImage_" + format);
        mJLabel = new JLabel("Create 4 HomePage images", JLabel.CENTER);
        jFrame.add(mJLabel, BorderLayout.CENTER);

        JPanel btnPanel = new JPanel();
        JButton actionButton = new JButton("Action");
        actionButton.setPreferredSize(new Dimension(100, 20));
        btnPanel.add(actionButton);
        jFrame.add(btnPanel, BorderLayout.SOUTH);


        jFrame.setBounds(FRAME_LEFT, FRAME_TOP, FRAME_RIGHT, FRAME_BOTTOM);
        jFrame.setVisible(true);
        jFrame.setResizable(false);//窗口大小不可调
        jFrame.setLocationRelativeTo(null);//居中
        jFrame.validate();
        jFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        addWindowListener(new WindowAdapter() {
            public void windowClosing(WindowEvent e) {
                // super.windowClosing(e);
                System.exit(0);
            }
        });
        actionButton.addActionListener(this);

        startThread = new Thread(this);// 初始化进程
    }

    public void actionPerformed(ActionEvent e) {
        if (!startThread.isAlive()) { // 决定是否创建进程
            startThread = new Thread(this);
        }
        // 不要在UI线程外更新操作UI，这里SwingUtilities会找到UI线程并执行更新UI操作
        /*SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
            }
        });*/
        try {
            startThread.start(); // 启动进程
        } catch (Exception exp) { // 捕获异常
        }
    }

    public void run() {
        mJLabel.setText("Process ...");
        String currentDir = System.getProperty("user.dir");
        System.out.println("Current Folder:"+currentDir);

        String homePage_1920_1080_Path = currentDir + "\\" + HomePage_1920_1080;
        if (!new File(homePage_1920_1080_Path).exists()) {
            mJLabel.setText(HomePage_1920_1080 + " not exist");
            return;
        }

        BufferedImage bufferedImage = null;
        try {
            bufferedImage = ImageIO.read(new FileInputStream(homePage_1920_1080_Path));
        } catch (IOException e) {
            e.printStackTrace();
        }
        int width = bufferedImage.getWidth();
        int height = bufferedImage.getHeight();
        if (!(width == 1920 && height == 1080)) {
            mJLabel.setText("Please check the size of " + HomePage_1920_1080);
            return;
        }


        String vertical_BG_1920_3415_Path = RELEASE_COMPONENT_FOLDER + "\\" + Vertical_BG_1920_3415;
        if (!new File(vertical_BG_1920_3415_Path).exists()) {
            mJLabel.setText(Vertical_BG_1920_3415 + " not exist");
            return;
        }

        try {
            bufferedImage = ImageIO.read(new FileInputStream(vertical_BG_1920_3415_Path));
        } catch (IOException e) {
            e.printStackTrace();
        }
        width = bufferedImage.getWidth();
        height = bufferedImage.getHeight();
        if (!(width == 1920 && height == 3415)) {
            mJLabel.setText("Please check the size of " + Vertical_BG_1920_3415);
            return;
        }

        boolean zoomImageResult01 = zoomImage(HomePage_1920_1080, 1146, 717, HomePage_Bilibili_1146_717);
        if (!zoomImageResult01) {
            mJLabel.setText("Zoom " + HomePage_Bilibili_1146_717 + " failed!");
            return;
        }
        boolean zoomImageResult02 = zoomImage(HomePage_1920_1080, 1075, 717, HomePage_Sohu_1075_717);
        if (!zoomImageResult02) {
            mJLabel.setText("Zoom " + HomePage_Sohu_1075_717 + " failed!");
            return;
        }
        boolean mergeImage01 = mergeImage(HomePage_1920_1080, Vertical_BG_1920_3415, HomePage_1920_3415_Vertical);
        if (!mergeImage01) {
            mJLabel.setText("Merge " + HomePage_1920_3415_Vertical + " failed!");
            return;
        }
        boolean zoomImageResult03 = zoomImage(HomePage_1920_3415_Vertical, 1080, 1920, HomePage_1080_1920_Vertical);
        if (!zoomImageResult03) {
            mJLabel.setText("Zoom " + HomePage_1080_1920_Vertical + " failed!");
            return;
        }
        mJLabel.setText("done");
    }

    private boolean mergeImage(String image1, String image2, String newImage) {
        String currentDir = System.getProperty("user.dir");
        String image1Path = currentDir + "\\" + image1;
        if (!new File(image1Path).exists()) {
            return false;
        }
        String image2Path = currentDir + "\\" + image2;
        if (Vertical_BG_1920_3415.equalsIgnoreCase(image2)) {
            image2Path = RELEASE_COMPONENT_FOLDER + "\\Vertical_BG_1920_3415.jpg";
        }
        if (!new File(image2Path).exists()) {
            return false;
        }
        MergeImageUtils mergeImageUtils = new MergeImageUtils();
		BufferedImage mergedImage = mergeImageUtils.mergeImagetogeter(mergeImageUtils.loadImageLocal(image1),
				mergeImageUtils.loadImageLocal(image2Path));
        mergeImageUtils.writeImageLocal(System.getProperty("user.dir") + "\\" + newImage, mergedImage);
        return true;
    }

    private boolean zoomImage(String sourceImage, int width, int height, String newImageName) {
        String currentDir = System.getProperty("user.dir");
        String sourceImagePath = currentDir + "\\" + sourceImage;
        if (!new File(sourceImagePath).exists()) {
            return false;
        }
        ZoomPicUtils picUtils = null;
        try {
            picUtils = new ZoomPicUtils(sourceImagePath, newImageName);
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            picUtils.zoomBySize(width, height);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }
}
