package com.test.android.ui;


import com.test.android.SomeFileUtils;
import com.test.android.common.Utils;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.GridLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.File;

import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;

class CopyWxQQAccountUI extends J<PERSON>rame implements Runnable, ActionListener {
    private static final long serialVersionUID = 1L;
    private final JFrame frame = new JFrame("测试");
    private final JPanel inputPanel = new JPanel(new GridLayout(2, 2, 20, 20));//2行2列 水平间距20 垂直间距10
    private final JPanel btnPanel = new JPanel();
    final JTextField wxPathField = new JTextField();
    final JTextField qqPathField = new JTextField();

    private Thread startThread;// 声明一个线程
    private int FRAME_LEFT = 380, FRAME_TOP = 120, FRAME_RIGHT = 366, FRAME_BOTTOM = 135;

    public CopyWxQQAccountUI() {
        startThread = new Thread(this);// 初始化进程

        //setLayout(new FlowLayout());

        JLabel wxPathLabel = new JLabel("路径1：", JLabel.CENTER);
        //wxPathLabel.setHorizontalAlignment(SwingConstants.LEFT);
        //wxPathLabel.setBounds(FRAME_LEFT + 30, FRAME_TOP + 30, FRAME_LEFT + 80, FRAME_TOP + 80);
        //wxPathField.setColumns(15);

        inputPanel.add(wxPathLabel);
        wxPathField.setPreferredSize(new Dimension(200, 30));
        //wxPathField.setHorizontalAlignment(SwingConstants.LEFT);

        inputPanel.add(wxPathField);

        JLabel qqPathLabel = new JLabel("路径2：", JLabel.CENTER);
        //qqPathLabel.setHorizontalAlignment(SwingConstants.LEFT);
        //qqPathLabel.setBounds(FRAME_LEFT + 30, FRAME_TOP + 30, FRAME_LEFT + 80, FRAME_TOP + 80);
        //wxPathField.setColumns(15);
        //qqPathField.setHorizontalAlignment(SwingConstants.LEFT);
        inputPanel.add(qqPathLabel);
        qqPathField.setPreferredSize(new Dimension(200, 30));
        inputPanel.add(qqPathField);

        GridLayout grid = new GridLayout(2, 2);//网格布局
        inputPanel.setLayout(grid);

        //setLayout（new Flowlayout（））

        //frame.add(inputPanel, BorderLayout.WEST);
        frame.add(inputPanel);

        //frame.setLayout(new FlowLayout());//流式布局

        JButton actionButton = new JButton("运行");
        btnPanel.add(actionButton);
        frame.add(btnPanel, BorderLayout.SOUTH);

        //frame.setBounds(666, 555, 175, 135);
        frame.setBounds(FRAME_LEFT, FRAME_TOP, FRAME_RIGHT, FRAME_BOTTOM);
        frame.setVisible(true);
        //frame.setResizable(false);//窗口大小不可调
        frame.setLocationRelativeTo(null);//居中
        frame.validate();
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        addWindowListener(new WindowAdapter() {
            public void windowClosing(WindowEvent e) {
                // super.windowClosing(e);
                System.exit(0);
            }
        });
        actionButton.addActionListener(this);
    }


    /*private final String[] WeChatFilesPaths = {"H:\\software_installed\\Tencent\\WeChat\\data\\WeChat Files"};
    private final String[] QQFilesPaths = {"C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\Users"};*/

    private final String[] WeChatFilesPaths = {"E:\\develop\\Studio_WorkPlace\\TestAndroid\\test\\wx1"};
    private final String[] QQFilesPaths = {"E:\\develop\\Studio_WorkPlace\\TestAndroid\\test\\QQ"};

    public void actionPerformed(ActionEvent e) {
        // startButton的响应事件
        if (!startThread.isAlive()) { // 决定是否创建进程
            startThread = new Thread(this);
        }
        try {
            startThread.start(); // 启动进程
        } catch (Exception exp) { // 捕获异常
        }
    }

    public void run() {
        String wxFieldPath = wxPathField.getText().toString().trim();
        String qqFieldPath = qqPathField.getText().toString().trim();

        String userDir = System.getProperties().getProperty("user.dir");
        System.out.println("userDir:" + userDir);

        String wxSrcDataPath = null;
        for (String path : WeChatFilesPaths) {
            if (new File(path).exists()) {
                wxSrcDataPath = path;
                break;
            }
        }
        // 优先调用输入框的地址。
        if (wxFieldPath != null && !"".equals(wxFieldPath)) {
            wxSrcDataPath = wxFieldPath;
        }
        if (wxSrcDataPath == null || !new File(wxSrcDataPath).exists()) {
            // System.out.println("do not find wx folder.");
            JOptionPane.showMessageDialog(frame, "do not find wx folder."); // 弹出提示窗口
            // return;
        } else {
            copyWxData(wxSrcDataPath, userDir);
            try {
                SomeFileUtils.dirDelete(wxSrcDataPath);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        String qqSrcDataPath = null;
        for (String path : QQFilesPaths) {
            if (new File(path).exists()) {
                qqSrcDataPath = path;
                break;
            }
        }
        // 优先调用输入框的地址。
        if (qqFieldPath != null && !"".equals(qqFieldPath)) {
            qqSrcDataPath = qqFieldPath;
        }
        if (qqSrcDataPath == null || !new File(qqSrcDataPath).exists()) {
            // System.out.println("do not find wx folder.");
            JOptionPane.showMessageDialog(frame, "do not find QQ folder."); // 弹出提示窗口
            // return;
        } else {
            copyQQData(qqSrcDataPath, userDir);
            try {
                SomeFileUtils.dirDelete(qqSrcDataPath);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private void copyQQData(String qqSrcDataPath, String userDir) {
        //SimpleDateFormat dateFormat = new SimpleDateFormat("YYYYMMdd_HHmmss");
        //String startDate = dateFormat.format(new Date());
        String qqDesDataPath = userDir + "\\data\\QQ";
        Utils.createEnableDir(qqDesDataPath);

        File[] listFiles = new File(qqSrcDataPath).listFiles();
        if (listFiles != null && listFiles.length > 0) {
            for (File itemFile : listFiles) {
                // 遍历所有的图片
                if (itemFile.isDirectory()) {
                    // 如果是目录的话，则需要循环遍历。
                    // recurseMoveFileToDirectory(itemFile);

                    //文件夹的名称，即为QQ账号
                    Utils.createEnableDir(qqDesDataPath + "\\" + itemFile.getName());
                    //暂时不复制文件
                        /*if (new File(itemFile.getPath() + "\\Files").exists()) {
                            try {
                                SomeFileUtils.dirCopy(itemFile.getPath() + "\\Files", qqDesDataPath + "\\" + itemFile.getName() + "\\Files");
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                        } else {
                            System.out.println(itemFile.getName() + ": do not have a Files Folder.");
                        }*/
                } else {
                    //moveFileToDirectory(itemFile);
                }
            }
        } else {
            System.out.println("do not find QQ account folder.");
        }
    }

    private void copyWxData(String wxSrcDataPath, String userDir) {
        // SimpleDateFormat dateFormat = new SimpleDateFormat("YYYYMMdd_HHmmss");
        // String startDate = dateFormat.format(new Date());
        String wxDesDataPath = userDir + "\\data\\wx";
        Utils.createEnableDir(wxDesDataPath);

        File[] listFiles = new File(wxSrcDataPath).listFiles();
        if (listFiles != null && listFiles.length > 0) {
            for (File itemFile : listFiles) {
                // 遍历所有的图片
                if (itemFile.isDirectory() && !itemFile.getName().contains("All Users") && !itemFile.getName().contains("QQBrowser Plugin")) {
                    //如果是目录的话，则需要循环遍历。
                    //recurseMoveFileToDirectory(itemFile);

                    //文件夹的名称，即为微信账号
                    Utils.createEnableDir(wxDesDataPath + "\\" + itemFile.getName());
                    if (new File(itemFile.getPath() + "\\Files").exists()) {
                        try {
                            SomeFileUtils.dirCopy(itemFile.getPath() + "\\Files", wxDesDataPath + "\\" + itemFile.getName() + "\\Files");
                        } catch (Exception e1) {
                            e1.printStackTrace();
                        }
                    } else {
                        System.out.println(itemFile.getName() + ": do not have a Files Folder.");
                    }
                } else {
                    //moveFileToDirectory(itemFile);
                }
            }
        } else {
            System.out.println("do not find wx account folder.");
        }
    }
}

public class CopyWxQQAccount {
    public static void main(String[] args) {
        new CopyWxQQAccountUI();
    }
}