package com.test.android.api;

import java.util.concurrent.CountDownLatch;

public class ApiMain {
    public static void main(String[] args) {
        testSingleton();
    }

    private static void testSingleton() {
        final CountDownLatch latch = new CountDownLatch(1);
        int threadCount = 1000;
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                try {
                    latch.await();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                System.out.println(Singleton.getInstance().hashCode());
            }).start();
        }
        latch.countDown();
    }
}
