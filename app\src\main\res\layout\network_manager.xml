<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >

    <Button
        android:id="@+id/btn_network_open"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开启移动网络" />

    <Button
        android:id="@+id/btn_network_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="关闭移动网络" />

    <Button
        android:id="@+id/btn_wifi_open"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开启WiFi网络" />

    <Button
        android:id="@+id/btn_wifi_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="关闭WiFi网络" />

    <Button
        android:id="@+id/btn_fly_open"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开启飞行模式" />

    <Button
        android:id="@+id/btn_fly_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="关闭飞行模式" />

</LinearLayout>