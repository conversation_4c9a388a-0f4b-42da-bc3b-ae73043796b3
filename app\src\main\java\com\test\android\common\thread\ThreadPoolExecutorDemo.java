package com.test.android.common.thread;

import java.util.Queue;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @todo how to use ThreadPoolExecutor when multi-thread.
 * @time 2017/10/18 18:17
 */
public class ThreadPoolExecutorDemo {
    public void createThreadPool() {
        /**
         * 队列深度
         */
        int queueDeep = 5;
        /*
         * 创建线程池，最小线程数为2，最大线程数为4，线程池维护线程的空闲时间为3秒，
         * 使用队列深度为5的有界队列，如果执行程序尚未关闭，则位于工作队列头部的任务将被删除，
         * 然后重试执行程序（如果再次失败，则重复此过程），里面已经根据队列深度对任务加载进行了控制。
         */
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(2, 4, 3, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(queueDeep),
                new ThreadPoolExecutor.DiscardOldestPolicy());
        // 向线程池中添加 10 个任务
        int taskSize = 10;
        for (int i = 0; i < taskSize; i++) {
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (getQueueSize(threadPoolExecutor.getQueue()) >= queueDeep) {
                //循环检测，如果size大于5，则等待3秒。
                System.out.println("准备添加" + i + "，但队列已满，等3秒再添加任务");
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            TaskThreadPoolRunnable taskThreadPoolRunnable = new TaskThreadPoolRunnable(i);
            System.out.println("already put i:" + i);
            threadPoolExecutor.execute(taskThreadPoolRunnable);
        }
        threadPoolExecutor.shutdown();
    }

    private synchronized int getQueueSize(Queue queue) {
        return queue.size();
    }

    public static void main(String[] args) {
        ThreadPoolExecutorDemo test = new ThreadPoolExecutorDemo();
        test.createThreadPool();
    }

    class TaskThreadPoolRunnable implements Runnable {
        private int index;

        public TaskThreadPoolRunnable(int index) {
            this.index = index;
        }

        @Override
        public void run() {
            System.out.println(Thread.currentThread() + " index:" + index);
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}