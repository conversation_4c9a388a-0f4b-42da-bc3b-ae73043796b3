package com.test.android.file;


import com.test.android.SomeFileUtils;

import java.io.File;

/**
 * remove the second layer folder when two same name folders nesting.
 * <p>
 * example:
 * <p>
 * C:/folder01/folder01/test.zip
 * <p>
 * after convert:
 * <p>
 * C:/folder01/test.zip
 */
public final class RemoveTheSameFolderLayer {
    public static final String BASE_PATH = "H:\\AutoSmart\\jq\\DoWithSmart\\WeMedia\\YTB\\Free_Music\\Adobe\\Music Loops & Beds";

    public static void main(String[] args) {
        File baseDir = new File(BASE_PATH);
        if (!baseDir.exists()) {
            System.out.println("PLEASE INPUT RIGHT BASE PATH.");
        }
        if (!baseDir.isDirectory()) {
            System.out.println("PLEASE INPUT A DIRECTORY PATH..");
        }
        File[] listFiles = baseDir.listFiles();
        for (File eachFile : listFiles) {
            File[] listFiles2 = eachFile.listFiles();
            if (listFiles2 != null && listFiles2.length == 1) {
                //可能存在两层，相同文件夹名称的嵌套
                if (listFiles2[0].getName().equalsIgnoreCase(eachFile.getName())) {
                    //规避第三层目录中，有文件夹的名称与第二层文件夹名称相同的情况.
                    File tempDir = new File(listFiles2[0].getParent(), "temp2020");
                    listFiles2[0].renameTo(tempDir);
                    try {
                        SomeFileUtils.dirCopy(tempDir.getPath(), eachFile.getPath());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        SomeFileUtils.dirDelete(tempDir.getPath());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    System.out.println("DELETE:" + tempDir.getParent());
                }
            }
        }


        //delete copyed files
        /*File baseDir = new File(BASE_PATH);
        if (!baseDir.exists()) {
            System.out.println("PLEASE INPUT RIGHT BASE PATH.");
        }
        if (!baseDir.isDirectory()) {
            System.out.println("PLEASE INPUT A DIRECTORY PATH..");
        }
        File[] listFiles = baseDir.listFiles();
        for (File eachFile : listFiles) {
            File[] listFiles2 = eachFile.listFiles();
            if (listFiles2 != null *//*&& listFiles2.length == 1*//*) {
                //可能存在两层，相同文件夹名称的嵌套
                for (File eachFile2 : listFiles2) {
                    if (eachFile2.getName().equalsIgnoreCase(eachFile.getName())) {
                        try {
                            SomeFileUtils.dirDelete(eachFile2.getPath());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }*/


    }


}