package com.test.android.jxbycg

/**
 * 记录为每种尺寸的折边件下料的整块彩钢板，加工为折边件的明细以及余料明细
 *
 * 注意：这里记录的是整块彩钢板的加工明细与余料明细
 */
class BlankingColorPlateModel {

    constructor()

    constructor(blankingCPLength: Float, blankingCPWidth: Int, blankingCPAmount: Int) {
        this.blankingCPLength = blankingCPLength
        this.blankingCPWidth = blankingCPWidth
        this.blankingCPAmount = blankingCPAmount
    }

    constructor(blankingCPLength: Float, blankingCPWidth: Int, blankingCPAmount: Int, edgeFoldInfos: MutableList<EdgeFoldInfo>, extraColorPlates: MutableList<ExtraColorPlate>) {
        this.blankingCPLength = blankingCPLength
        this.blankingCPWidth = blankingCPWidth
        this.blankingCPAmount = blankingCPAmount
        this.edgeFoldInfos = edgeFoldInfos
        this.extraColorPlates = extraColorPlates
    }


    /**
     * 下料彩钢板的长度
     */
    var blankingCPLength: Float = 0.0f

    /**
     * 下料彩钢板的宽度
     */
    var blankingCPWidth = 0

    /**
     * 下料彩钢板的块数
     */
    var blankingCPAmount = 0

    /**
     * 已下料彩钢板加工为折边的明细
     */
    var edgeFoldInfos = mutableListOf<EdgeFoldInfo>()


    /**
     * 已下料彩钢板的余料明细
     */
    var extraColorPlates = mutableListOf<ExtraColorPlate>()
}