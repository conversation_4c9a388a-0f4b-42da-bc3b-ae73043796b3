package com.common.utils;

import android.content.Context;
import android.telephony.TelephonyManager;

import java.lang.reflect.Method;

/**
 * Created by wangbing on 2017/4/2.
 */
public class PhoneInfoUtils {
    /**
     * 获取IMEI
     *
     * @param context
     * @return
     */
    public static String getIMEI(Context context) {
        try {
            TelephonyManager tm = (TelephonyManager) (context.getSystemService(Context.TELEPHONY_SERVICE));
            String imei = tm == null ? "" : (tm.getDeviceId() == null ? "" : tm.getDeviceId());
            Logs.i("DeviceAppUtils", "IMEI:" + imei);
            return imei;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取M1(注，需要基于数据中心Android新设备标识符SDK)
     *
     * @param context
     * @return
     */
    /*public static String getM1(Context context) {
        String m1 = QHDevice.getDeviceId(context, QHDevice.DataType.IMEI);
        Log.i("DeviceAppUtils", "M1:" + m1);
        return m1;
    }*/

    /**
     * 获取M2，用于识别安卓设备;当APP被卸载时，这个M2码会发生改变。
     *
     * @param context
     * @return
     */
    public static String getM2(Context context) {
        try {
            TelephonyManager tm = (TelephonyManager) (context.getSystemService(Context.TELEPHONY_SERVICE));
            String imei = tm == null ? "" : (tm.getDeviceId() == null ? "" : tm.getDeviceId());
            String androidId = android.provider.Settings.System.getString(context.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);
            String serialNo = getDeviceSerial();
            String sImei2 = NumberStringUtils.md5("" + imei + androidId + serialNo);
            Logs.i("DeviceAppUtils", "M2:" + sImei2);
            return sImei2;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    private static String getDeviceSerial() {
        String serial = null;
        try {
            Class<?> c = Class.forName("android.os.SystemProperties");
            Method get = c.getMethod("get", String.class);
            serial = (String) get.invoke(c, "ro.serialno");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return serial;
    }


}
