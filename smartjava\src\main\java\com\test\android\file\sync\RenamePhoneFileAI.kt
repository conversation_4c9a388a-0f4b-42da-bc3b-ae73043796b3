package com.test.android.file.sync

import com.bingo.core.LoggerRateLimiter
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.system.exitProcess

/**
 * 从手机导出的图片/视频/录音等文件，把日期放到文件名的最前面，这样在听的时候，就可以按照时间顺序;
 * 对同一个目录，需要多次执行此程序，直到提示已处理的文件数量为0为止;
 *
 * To Rename File Names List:
 */
fun main() {
    doRenamePhoneFileAI()
}

fun doRenamePhoneFileAI() {
    if (isRenamePhoneFileTestMode()) {
        val testCases = listOf(
            "IMG_20230718_173308.jpg",
            "VID_20230718_173308.jpg",
            "video_20200509_135956.mp4",
            "Screenshot_20230718_173308.jpg",
            "Snipaste_2024-06-15_02-08-23.jpg",
            "PXL_20231207_070023670.MP.jpg",
            "MTXX_20240818_115450424.jpg",
            "TG-2023-07-20-153016135.mp4",
            ".VID_20241025_211522.mp4",
            "Bingo_1669978073369.mp3",
            "mmexport1704636319595.mp4",
            "mmqrcode1719185320381.png",
            "mmscreenshot1733066008010.png",
            "ECommerce1704636319595.png",
            "tb_image_share_1718702888928.jpg.png",
            "1706958689231.jpg",
            "1534133069992_1502948582.jpg",
            "Bingo(18600598523)_20240316185002.mp3",
            "20240307170758_13807068831(13807068831).mp3",
            "2024051515070181.jpg",
            "IMG20231024101658.jpg",
            "2022-11-22-22-03-25-263_com.miui.home_Screenshot.jpg",
            "2013_07_12_11_12_18.jpg",
            "2023-11-18-082229223-TG.mp4",
            "2023-12-19_163221_cloud_video.mp4",
            "20240615_02-08-23_Snipaste.png",
            "2022-11-20_20-03-56_838.mp4",
            "Screenrecorder-2024-03-31-19-49-48-248.mp4",
            "Record_2024-06-08-01-18-52.mp4",
            "2024-11-26_22-21-02.mp4",
            "Screenshot_2025-03-24-09-59-08-510_com.tencent.mm.jpg",
            "微信录音 _20250326082658.aac",
            ".wx_camera_1738719875391.mp4"
        )
        for (testCase in testCases) {
            println("$testCase -> ${convertFileName(testCase)}")
        }
    } else {
        var fileList = mutableListOf<String>()
        formatFileDateNameAI(File(getSourcePath()), fileList)
        println("======doRenamePhone files count: " + fileList.size + "======\n")
        if (fileList.isNotEmpty()) { // 有些文件名需要连续转换多次
            doRenamePhoneFileAI()
        } else {
            // 添加用户名后缀
            if (getUserName().isEmpty()) {
                return
            }
            var fileMap = mutableMapOf<String, Boolean>()
            addUserNameSuffix(File(getSourcePath()), fileMap)
            val trueCount = fileMap.values.count { it }
            val falseCount = fileMap.size - trueCount
            println("======addUserNameSuffix---Success:$trueCount,Fail:$falseCount======\n")
        }
    }
}


/**
 * 在文件名末尾添加用户名后缀
 * 20231028_101658_IMG.jpg -> 20231028_101658_IMG_Bingo.jpg
 */
fun addUserNameSuffix(directory: File, fileMap: MutableMap<String, Boolean>) {
    if (directory.isDirectory) {
        val files = directory.listFiles()
        files?.forEach { file ->
            if (file.isDirectory) {
                addUserNameSuffix(file, fileMap)
            } else {
                val fileName = file.name
                // 打印所有遍历的文件
                // println("------${file.path}------")

                // 判断文件名是否以任一用户名作为后缀
                if (getAllUserNames().any { file.nameWithoutExtension.endsWith("_$it") }) {
                    return@forEach
                }

                var addSuffixFileName = ""
                try {
                    addSuffixFileName = addSuffix(fileName)
                } catch (exception: Exception) {
                    println("------${file.path}------")
                    println("Exception: >>>>>> $fileName --> $addSuffixFileName <<<<<<")
                    println(exception.toString())
                    exitProcess(0)
                }
                if (!fileName.equals(addSuffixFileName)) {
                    LoggerRateLimiter.logIfAllowed { "$fileName --> $addSuffixFileName" }
                    val newFile = File(directory.absolutePath + File.separator + addSuffixFileName)
                    fileMap.put(newFile.path, false)
                    if (file.renameTo(newFile)) {
                        fileMap.put(newFile.path, true)
                    }
                }
            }
        }
    }
}


/*文件重命名需要达到的标准：
RenamePhoneFile_Test_Standard.txt
当前开发过程中的具体情况：
RenamePhoneFile_Test_Now.txt*/


fun formatFileDateNameAI(directory: File, fileList: MutableList<String>) {
    if (directory.isDirectory) {
        val files = directory.listFiles()
        files?.forEach { file ->
            if (file.isDirectory) {
                formatFileDateNameAI(file, fileList)
            } else {
                val fileName = file.name
                // 打印所有遍历的文件
                // println("------${file.path}------")

                /*if (fileName.startsWith(".")) {
                    return@forEach
                }*/
                var newFileName = ""
                try {
                    newFileName = convertFileName(fileName)
                } catch (exception: Exception) {
                    println("------${file.path}------")
                    println("Exception: >>>>>> $fileName --> $newFileName <<<<<<")
                    println(exception.toString())
                    exitProcess(0)
                }
                if (!fileName.equals(newFileName)) {
                    LoggerRateLimiter.logIfAllowed { "$fileName --> $newFileName" }
                    val newFile = File(directory.absolutePath + File.separator + newFileName)
                    file.renameTo(newFile)
                    fileList.add(newFile.name)
                }
            }
        }
    }
}


fun convertFileName(originalName: String): String {
    val patterns = listOf(
        // .VID_20241025_211522.mp4 -> VID_20241025_211522.mp4 (Edge case, remove leading dot)
        """^\.(.*)$""" to "$1",
        // IMG_20230718_173308.jpg -> 20230718_173308_IMG.jpg
        // VID_20230718_173308.jpg -> 20230718_173308_VID.jpg
        // Screenshot_20230718_173308.jpg -> 20230718_173308_Screenshot.jpg
        // video_20200509_135956.mp4 -> 20200509_135956_video.mp4
        """^([^0-9]+)_(\d{8})_(\d{6})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$2_$3_$1.$4",
        // Screenshot_20250419-163758.png -> 20250419_163758_Screenshot.png
        """^([^0-9]+)_(\d{8})-(\d{6})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$2_$3_$1.$4",
        // 扫描全能王 2025-03-10 11.58.jpg -> 20250310_1158_扫描全能王.jpg
        """^([^0-9]+) (\d{4})-(\d{2})-(\d{2}) (\d{2})\.(\d{2})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$2$3$4_$5$6_$1.$7",
        // 微信录音 _20250326082658.aac -> 20250326_082658_微信录音 .aac
        // Bingo(18600598523)_20240316185002.mp3 -> 20240316_185002_Bingo(18600598523).mp3
        // """^(.*?)\((\d+)\)_(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$3$4$5_$6$7$8_$1($2).$9",
        """^(.*?)_(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$2$3$4_$5$6$7_$1.$8",
        // IMG20231024101658.jpg -> 20231024_101658_IMG.jpg
        """^([^0-9]+)(\d{8})(\d{6})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$2_$3_$1.$4",


        // Snipaste_2024-06-15_02-08-23.jpg -> 20240615_020823_Snipaste.jpg
        """^([^0-9]+)_(\d{4}-\d{2}-\d{2})_(\d{2}-\d{2}-\d{2})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to { m: MatchResult ->
            "${m.groupValues[2].replace("-", "")}_${m.groupValues[3].replace("-", "")}_${m.groupValues[1]}.${m.groupValues[4]}"
        },
        // Record_2024-06-08-01-18-52.mp4 转换为 20240608_011852_Record.mp4
        """^(Record)_(\d{4}-\d{2}-\d{2})-(\d{2}-\d{2}-\d{2})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to { m: MatchResult ->
            "${m.groupValues[2].replace("-", "")}_${m.groupValues[3].replace("-", "")}_${m.groupValues[1]}.${m.groupValues[4]}"
        },

        // PXL_20231207_070023670.MP.jpg -> 20231207_070023670.MP_PXL.jpg
        """^([^0-9]+)_(\d{8})_(\d{6})(\d{3})\.(MP)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$2_$3_$4.$5_$1.$6",  // Added 'MP' group
        // MTXX_20240818_115450424.jpg -> 20240818_115450_424_MTXX.jpg
        // PXL_20250417_061950061.jpg -> 20250417_061950_061_PXL.jpg
        """^([^0-9]+)_(\d{8})_(\d{6})(\d{3})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$2_$3_$4_$1.$5",
        // TG-2023-07-20-153016135.mp4 -> 20230720_153016135-TG.mp4
        """^(TG)-(\d{4}-\d{2}-\d{2})-(\d+)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$2-$3-$1.$4",

        // Bingo_1742441466258.mp3 -> 20250320_113106_258_Bingo.mp3
        """^(.+)_(\d{13})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to { m: MatchResult -> timestampAndStr(m) },

        // mmscreenshot1733066008010.png -> 20241201_231328_010_mmscreenshot.png
        """^([^0-9]*)(\d{13})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to { m: MatchResult -> timestampAndStr(m) },

        // tb_image_share_1718702888928.jpg.png -> 20240618_172808_tb_image_share.png
        """^(tb_image_share)_(\d{13})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to { m: MatchResult ->
            timestampToDateStr(m, 1, 2, "tb_image_share", extension = 4)
        },

        // 1534133069992_1502948582.jpg -> 20180813_120429_992_1502948582.jpg
        """^(\d{13})_(\d+)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to { m: MatchResult -> timestampAndOtherNumber(m) },
        // 20240307170758_13807068831(13807068831).mp3 转换为 20240307_170758_13807068831(13807068831).mp3
        """^(\d{8})(\d{6})_(.*)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$1_$2_$3.$4",
        // 2024051515070181.jpg -> 20240515_150701_81.jpg`
        """^(\d{8})(\d{6})(\d{2})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$1_$2_$3.$4",
        // 2022-11-22-22-03-25-263_com.miui.home_Screenshot.jpg -> 20221122_220325-263_com.miui.home_Screenshot.jpg
        """^(\d{4}-\d{2}-\d{2})-(\d{2}-\d{2}-\d{2})-(\d+)_([^_]+)_(.*)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to { m: MatchResult ->
            "${m.groupValues[1].replace("-", "")}_${m.groupValues[2].replace("-", "")}-${m.groupValues[3]}_${m.groupValues[4]}_${m.groupValues[5]}.${m.groupValues[6]}"
        },
        // 2013_07_12_11_12_18.jpg 转换为 20130712_111218.jpg
        """^(\d{4})_(\d{2})_(\d{2})_(\d{2})_(\d{2})_(\d{2})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to "$1$2$3_$4$5$6.$7",
        // 2023-11-18-082229223-TG.mp4 to 20231118_082229223-TG.mp4
        """^(\d{4}-\d{2}-\d{2})-(\d+)-(TG)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to { m: MatchResult ->
            "${m.groupValues[1].replace("-", "")}_${m.groupValues[2]}-${m.groupValues[3]}.${m.groupValues[4]}"
        },
        // 2023-12-19_163221_cloud_video.mp4 转换为 20231219_163221_cloud_video.mp4
        """^(\d{4}-\d{2}-\d{2})_(\d{6})_(.*)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to { m: MatchResult ->
            "${m.groupValues[1].replace("-", "")}_${m.groupValues[2]}_${m.groupValues[3]}.${m.groupValues[4]}"
        },
        // 20240615_02-08-23_Snipaste.png 转换为 20240615_020823_Snipaste.png
        """^(\d{4})(\d{2})(\d{2})_(\d{2})-(\d{2})-(\d{2})_(.*)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to "$1$2$3_$4$5$6_$7.$8",
        // 2022-11-20_20-03-56_838.mp4 转换为 20221120_200356_838.mp4
        """^(\d{4}-\d{2}-\d{2})_(\d{2}-\d{2}-\d{2})_(\d+)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to { m: MatchResult ->
            "${m.groupValues[1].replace("-", "")}_${m.groupValues[2].replace("-", "")}_${m.groupValues[3]}.${m.groupValues[4]}"
        },

        // Screenrecorder-2024-03-31-19-49-48-248.mp4 转换为 20240331_194948_248-Screenrecorder.mp4
        """^(Screenrecorder)-(\d{4}-\d{2}-\d{2})-(\d{2}-\d{2}-\d{2})-(\d+)\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to { m: MatchResult ->
            "${m.groupValues[2].replace("-", "")}_${m.groupValues[3].replace("-", "")}_${m.groupValues[4]}-${m.groupValues[1]}.${m.groupValues[5]}"
        },

        // Screenshot_2025-03-24-09-59-08-510_com.tencent.mm.jpg 转换为 20250324_095908_510_com.tencent.mm_Screenshot.jpg
        """^([^0-9]*)_(\d{4})-(\d{2})-(\d{2})-(\d{2})-(\d{2})-(\d{2})-(\d{3})_(.+)\.(jpg)$""" to "$2$3$4_$5$6$7_$8_$9_$1.$10",

        // 2024-11-26_22-21-02.mp4 -> 20241126_222102.mp4
        """^(\d{4}-\d{2}-\d{2})_(\d{2}-\d{2}-\d{2})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)""" to { m: MatchResult ->
            "${m.groupValues[1].replace("-", "")}_${m.groupValues[2].replace("-", "")}.${m.groupValues[3]}"
        },

        // FW4623412_02_20250521144640178.jpg 转换为 20250521_144640_178_FW4623412_02.jpg
        """^(FW[0-9]*)_(\d{2})_(\d{8})(\d{6})(\d{3})\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$""" to "$3_$4_$5_$1_$2.$6"
    )

    for ((pattern, replacement) in patterns) {
        val regex = Regex(pattern)
        if (regex.matches(originalName)) {
            // 输出匹配到的正则表达式
            // println(regex.toString())
            return when (replacement) {
                is String -> regex.replace(originalName, replacement)
                is Function1<*, *> -> (replacement as Function1<MatchResult, String>).invoke(regex.find(originalName)!!)
                else -> originalName // Should not happen, but handle for safety
            }
        }
    }
    return originalName // Return original if no pattern matches
}


fun addSuffix(originalName: String): String {
    val userName = getUserName()
    val regex = Regex("^(.*)\\.(aac|mp3|png|jpg|jpeg|mp4|avi|mov|mkv)$", RegexOption.IGNORE_CASE) // 添加忽略大小写

    return regex.find(originalName)?.let { matchResult ->
        val baseName = matchResult.groupValues[1]
        val extension = matchResult.groupValues[0].substringAfterLast(".") // 确保获取正确的扩展名
        "${baseName}_${userName}.$extension"
    } ?: originalName // Return original if no pattern matches
}

fun timestampAndStr(m: MatchResult): String {
    val timestamp = m.groupValues[2].toLongOrNull() ?: return m.groupValues[0]
    return if (timestamp > 31536000000 && timestamp < 4102444800000) { // Timestamp validity.
        val date = Date(timestamp)
        val sdfDate = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
        val sdfTime = SimpleDateFormat("HHmmss", Locale.getDefault())
        val milliseconds = String.format("%03d", timestamp % 1000)
        return if (m.groupValues[1].isEmpty()) {
            "${sdfDate.format(date)}_${sdfTime.format(date)}_${milliseconds}.${m.groupValues[3]}"
        } else {
            "${sdfDate.format(date)}_${sdfTime.format(date)}_${milliseconds}_${m.groupValues[1]}.${m.groupValues[3]}"
        }
    } else {
        return m.groupValues[0]
    }
}

fun timestampToDateStr(m: MatchResult, prefixIndex: Int = 1, timestampIndex: Int = 2, prefix: String, formatStr: String? = null, extension: Int = 3): String {
    val timestamp = m.groupValues[timestampIndex].toLongOrNull() ?: 0L
    val date = Date(timestamp)
    val sdfDate = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
    val sdfTime = SimpleDateFormat("HHmmss", Locale.getDefault())
    var millisecond = ""
    if (formatStr != null) {
        millisecond = String.format(formatStr, timestamp % 1000)
    }
    return "${sdfDate.format(date)}_${sdfTime.format(date)}$millisecond" + "_" + m.groupValues[prefixIndex] + "." + m.groupValues[extension]
}


// Modified function to handle the specific case
fun timestampAndOtherNumber(m: MatchResult): String {
    val timestamp = m.groupValues[1].toLongOrNull() ?: return m.value  // Return original if timestamp is invalid
    val otherNumber = m.groupValues[2]
    val extension = m.groupValues[3]

    // Check if it's a valid timestamp (roughly between 1970 and 2100)
    if (timestamp > 31536000000 && timestamp < 4102444800000) {
        val date = Date(timestamp)
        val sdfDate = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
        val sdfTime = SimpleDateFormat("HHmmss", Locale.getDefault())
        val formattedDate = sdfDate.format(date)
        val formattedTime = sdfTime.format(date)

        // Extract milliseconds correctly.  We take the *last* three digits of the timestamp.
        val milliseconds = String.format("%03d", timestamp % 1000)

        return "${formattedDate}_${formattedTime}_${milliseconds}_${otherNumber}.${extension}"
    } else {
        return m.value
    }
}

