package com.test.memoryleak;

import android.app.Activity;
import android.os.AsyncTask;
import android.os.Bundle;
import android.widget.TextView;

import com.squareup.leakcanary.RefWatcher;
import com.test.activity.MyApplication;
import com.test.android.R;

import java.lang.ref.WeakReference;

public class MemoryLeakActivity1 extends Activity {
    TextView textView;
    AsyncTask mAsyncTask;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.memory_leak_layout);
        textView = (TextView) findViewById(R.id.text_view);
        mAsyncTask = new BackgroundTask(new WeakReference<>(textView)).execute();
    }

    @Override
    protected void onDestroy() {
        /*if (mAsyncTask != null) {
            mAsyncTask.cancel(true);
        }*/
        super.onDestroy();
        /*RefWatcher refWatcher = MyApplication.getRefWatcher(this);
        refWatcher.watch(this);*/
    }

    private /*static */class BackgroundTask extends AsyncTask<Void, Void, String> {
        private final WeakReference<TextView> resultTextViewWr;

        public BackgroundTask(WeakReference<TextView> resultTextViewWr) {
            this.resultTextViewWr = resultTextViewWr;
        }

        @Override
        protected void onCancelled() {
            // Cancel mAsyncTask. Code omitted.
        }

        @Override
        protected String doInBackground(Void... params) {
            // Do background work. Code omitted.
            return "some string";
        }

        @Override
        protected void onPostExecute(String result) {
            TextView textView = resultTextViewWr.get();
            if (textView != null) {
                textView.setText(result);
            }
        }
    }
}
