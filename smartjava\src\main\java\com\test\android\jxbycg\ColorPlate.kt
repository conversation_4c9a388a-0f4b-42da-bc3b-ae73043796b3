package com.test.android.jxbycg

/**
 * 加工费:按平方计算 BySquare
 *
 * 加工费:一口价 FixedPrice
 */
class ColorPlate {

    constructor(colorPlateWidth: Int, colorPlatePriceWithTax: Float, processingChargeType: String, processingChargeWithTax: Float) {
        this.colorPlateWidth = colorPlateWidth
        this.colorPlatePriceWithTax = colorPlatePriceWithTax
        this.processingChargeType = processingChargeType
        this.processingChargeWithTax = processingChargeWithTax
    }


    /**
     * 单位 mm
     * 彩钢板的宽度，一般为1米或者1.2米
     */
    var colorPlateWidth = 1000

    /**
     * 彩钢板的含税单价
     */
    var colorPlatePriceWithTax: Float = 0.0f

    /**
     * 如果折边数量多，加工费:按平方计算 BySquare，一般为税前3.5元/平方。
     *
     * 如果折边数量少，加工费:一口价 FixedPrice，一般为每张彩钢板10块钱，手动输入总的加工费即可。
     */
    var processingChargeType: String

    /**
     * 含税加工费
     *
     * 比如:3.5表示折边的不含税加工费，除以0.9可以计算出含税的加工费
     */
    var processingChargeWithTax: Float = 0.0f
}