package com.test.android.file.sync

import com.bingo.core.LoggerRateLimiter
import java.io.File
import java.io.IOException


fun main() {
    doMoveDirStrucFiles()
}

fun doMoveDirStrucFiles() {
    val sourceDir = getSourcePath() + "StrucBC"
    val sourceDirFile = File(sourceDir)
    if (!sourceDirFile.exists() || !sourceDirFile.isDirectory) {
        println("错误: 源目录 $sourceDir 不存在或不是一个目录.")
        return
    }

    val baseDir = "H:\\Family\\${getUserName()}\\PhoneMediaSync"
    if (!File(baseDir).exists()) {
        println("错误: 无法找到 H:\\Family\\ 下的 ${getUserName()} 目录.")
        return
    }

    val targetDirDCIM = "$baseDir\\sdcard\\DCIM\\Camera"
    val targetDirScreenshots = "$baseDir\\sdcard\\DCIM\\Screenshots"
    val targetDirScreenRecorder = "$baseDir\\sdcard\\DCIM\\ScreenRecorder"
    val targetDirWeiXin = "$baseDir\\sdcard\\Pictures\\WeiXin"
    val targetDirAppRec = "$baseDir\\sdcard\\miui\\sound_recorder\\app_rec"
    val targetDirCallRec = "$baseDir\\sdcard\\miui\\sound_recorder\\call_rec"


    // 如果不存在，则创建目标目录
    File(targetDirDCIM).mkdirs()
    File(targetDirScreenshots).mkdirs()
    File(targetDirWeiXin).mkdirs()
    File(targetDirAppRec).mkdirs()
    File(targetDirCallRec).mkdirs()

    var fileMap = mutableMapOf<String, Boolean>()
    sourceDirFile.listFiles()?.forEach { file ->
        if (file.isFile) {
            if (file.length() > 0) {
                return@forEach
            }
            // 判断文件名是否以任一用户名作为后缀
            if (getAllUserNames().any { file.nameWithoutExtension.endsWith("_$it") }) {
                return@forEach
            }
            try {
                when {
                    (file.name.startsWith("IMG_") || file.name.startsWith("MTXX_") || file.name.startsWith("MEITU_") || file.name.startsWith("Danbo_")) && isImage(file) ->
                        moveFile(file, targetDirDCIM, fileMap)

                    // 2025030715304400.jpg  1741356786113.gif
                    (file.nameWithoutExtension.length == 16 || file.nameWithoutExtension.length == 13)
                            && file.nameWithoutExtension.all { it.isDigit() } && isImage(file) ->
                        moveFile(file, targetDirDCIM, fileMap)

                    (file.name.startsWith("VID_") || file.name.startsWith("TG-") || file.name.startsWith("lv_") || file.name.startsWith("MV_")) && isVideo(file) ->
                        moveFile(file, targetDirDCIM, fileMap)

                    file.name.startsWith("Screenshot_") && isImage(file) ->
                        moveFile(file, targetDirScreenshots, fileMap)

                    file.name.startsWith("Screenrecorder-") && isVideo(file) ->
                        moveFile(file, targetDirScreenRecorder, fileMap)

                    file.name.startsWith("wx_camera_") && (isImage(file) || isVideo(file)) || (file.name.startsWith("mmexport") && (isImage(file) || isVideo(file))) ->
                        moveFile(file, targetDirWeiXin, fileMap)

                    file.name.endsWith(".aac", ignoreCase = true) ->
                        moveFile(file, targetDirAppRec, fileMap)

                    file.name.endsWith(".mp3", ignoreCase = true) ->
                        moveFile(file, targetDirCallRec, fileMap)
                }
            } catch (e: IOException) {
                println("移动文件 ${file.name} 时发生错误: ${e.message}")
                return
            }
        }
    }
    val trueCount = fileMap.values.count { it }
    val falseCount = fileMap.size - trueCount
    println("======doMoveDirStrucFiles---Success:$trueCount,Fail:$falseCount======\n")
}

fun doMovePhoneFiles() {
    val sourceDir = getSourcePath()
    val sourceDirFile = File(sourceDir)
    if (!sourceDirFile.exists() || !sourceDirFile.isDirectory) {
        println("错误: 源目录 $sourceDir 不存在或不是一个目录.")
        return
    }

    val targetDirFile = File(getTargetPath())
    if (!targetDirFile.exists() || !targetDirFile.isDirectory) {
        println("错误: 目标目录 ${getTargetPath()} 不存在或不是一个目录.")
        return
    }

    var fileMap = mutableMapOf<String, Boolean>()
    sourceDirFile.listFiles()?.forEach { file ->
        if (file.isFile) {
            try {
                val yearStr = file.name.substring(0, 4)
                if (yearStr.all { it.isDigit() }) {
                    var targetFolderPath = getTargetPath() + "\\" + yearStr
                    if (!File(targetFolderPath).exists() || File(targetFolderPath).isFile) {
                        File(targetFolderPath).mkdirs()
                    }
                    // 判断文件名是否以用户名作为后缀
                    if (file.nameWithoutExtension.endsWith("_${getUserName()}")) {
                        moveFile(file, targetFolderPath, fileMap)
                    }
                } else {
                    return@forEach
                }
            } catch (e: IOException) {
                println("移动文件 ${file.name} 时发生错误: ${e.message}")
                return
            }
        }
    }
    val trueCount = fileMap.values.count { it }
    val falseCount = fileMap.size - trueCount
    println("======doMovePhoneFiles---Success:$trueCount,Fail:$falseCount======\n")
}

fun isImage(file: File): Boolean {
    val lowerCaseName = file.name.lowercase()
    return lowerCaseName.endsWith(".jpg") ||
            lowerCaseName.endsWith(".jpeg") ||
            lowerCaseName.endsWith(".png") ||
            lowerCaseName.endsWith(".gif")
}

fun isVideo(file: File): Boolean {
    val lowerCaseName = file.name.lowercase()
    return lowerCaseName.endsWith(".mp4") ||
            lowerCaseName.endsWith(".avi") ||
            lowerCaseName.endsWith(".mov")
}

fun moveFile(file: File, targetDir: String, fileMap: MutableMap<String, Boolean>) {
    val targetFile = File(targetDir, file.name)
    try {
        fileMap.put(file.path, false)
        if (file.renameTo(targetFile)) {
            LoggerRateLimiter.logIfAllowed { "移动 ${file.name} 到 $targetDir 成功" }
            fileMap.put(file.path, true)
        } else {
            LoggerRateLimiter.logIfAllowed { "移动 ${file.name} 到 $targetDir 失败" }
        }
    } catch (_: SecurityException) {
        // Handle cases where the program lacks permission to rename files.  This may require copying and deleting.
        println("权限错误: 无法移动 ${file.name}. 需要管理员权限.")
    } catch (e: IOException) {
        println("IOException: 移动 ${file.name} 失败: ${e.message}")
    }
}