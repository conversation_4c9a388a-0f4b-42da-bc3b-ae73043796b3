<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!--但是我们需要考虑非Lollipop设备。我们在res/drawable/selector.xml中定义一个没有动画的selector：-->
    <item android:state_activated="true">
        <bitmap android:src="@drawable/ic_heart_100"/>
    </item>
    <item android:state_activated="false">
        <bitmap android:src="@drawable/ic_heart_0"/>
    </item>
</selector>