<?xml version="1.0" encoding="utf-8"?>
<animation-list xmlns:android="http://schemas.android.com/apk/res/android"
                android:oneshot="true">

    <!--android:oneshot=”true”,这是animation-list的一个属性，表示播放完一次动画之后便停止动画。
    如果这个属性值设置为“false”，则动画会重复播放。-->

    <item
        android:drawable="@drawable/ic_heart_100"
        android:duration="500"/>

    <item
        android:drawable="@drawable/ic_heart_75"
        android:duration="500"/>

    <item
        android:drawable="@drawable/ic_heart_50"
        android:duration="500"/>

    <item
        android:drawable="@drawable/ic_heart_25"
        android:duration="500"/>

    <item
        android:drawable="@drawable/ic_heart_0"
        android:duration="500"/>

</animation-list>