apply plugin: 'java'
apply plugin: 'kotlin'

/*android {
    task makeJar(type: Copy) {
        //删除旧的jar包
        //delete 'build/libs/mysdk.jar'
        //源地址
        //from('build/intermediates/bundles/release/')
        from('src/main/java/com/test/android/ui/wemedia/')
        //导出jar包的地址
        into('build/libs/')
        //包含的jar包
        //include('classes.jar')
        //重命名jar包为’httpforgetandpost.jar’
        //rename('classes.jar', 'httpforgetandpost.jar')
    }
    makeJar.dependsOn(build)
}*/
dependencies {
    implementation 'com.volcengine:volcengine-java-sdk-ark-runtime:0.1.151'

    implementation("org.apache.pdfbox:pdfbox:2.0.29") // Or latest version, check Maven Central

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation files('libs/JsonUtils.jar')
    implementation files('libs/commons-io.jar')
    implementation files('libs/commons-codec-1.10.jar')
    implementation files('libs/guava-19.0.jar')
    implementation files('libs/registry.jar')
    //implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
}

sourceCompatibility = "1.8"
targetCompatibility = "1.8"

tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}
buildscript {
    ext.kotlin_version = '2.0.21'
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}
repositories {
    //mavenCentral()
    //google()
    //gradlePluginPortal()
}
compileKotlin {
    kotlinOptions {
        jvmTarget = "1.8"
    }
}
compileTestKotlin {
    kotlinOptions {
        jvmTarget = "1.8"
    }
}