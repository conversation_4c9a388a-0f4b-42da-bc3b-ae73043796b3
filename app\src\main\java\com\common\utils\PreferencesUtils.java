package com.common.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

public class PreferencesUtils {
    public static SharedPreferences mPreference;

    public static long getIntValue(Context context, String name, int defaultValue) {
        if (mPreference == null) {
            mPreference = PreferenceManager.getDefaultSharedPreferences(context);
        }
        return mPreference.getInt(name, defaultValue);
    }

    public static long getLongValue(Context context, String name, long defaultValue) {
        if (mPreference == null) {
            mPreference = PreferenceManager.getDefaultSharedPreferences(context);
        }
        return mPreference.getLong(name, defaultValue);
    }

    public static float getFloatValue(Context context, String name, float defaultValue) {
        if (mPreference == null) {
            mPreference = PreferenceManager.getDefaultSharedPreferences(context);
        }
        return mPreference.getFloat(name, defaultValue);
    }

    public static String getStringValue(Context context, String name, String defaultValue) {
        if (mPreference == null) {
            mPreference = PreferenceManager.getDefaultSharedPreferences(context);
        }
        return mPreference.getString(name, defaultValue);
    }

    public static void put(Context context, String name, Object value) {
        if (mPreference == null) {
            mPreference = PreferenceManager.getDefaultSharedPreferences(context);
        }
        SharedPreferences.Editor editor = mPreference.edit();
        if (value.getClass() == Boolean.class) {
            editor.putBoolean(name, (Boolean) value);
        } else if (value.getClass() == String.class) {
            editor.putString(name, (String) value);
        } else if (value.getClass() == Integer.class) {
            editor.putInt(name, (Integer) value);
        } else if (value.getClass() == Float.class) {
            editor.putFloat(name, (Float) value);
        } else if (value.getClass() == Long.class) {
            editor.putLong(name, (Long) value);
        }
        editor.commit();
    }

    public static void remove(Context context, String key) {
        if (mPreference == null) {
            mPreference = PreferenceManager.getDefaultSharedPreferences(context);
        }
        SharedPreferences.Editor editor = mPreference.edit();
        editor.remove(key);
        editor.commit();
    }

    /**
     * 此方法还需要进一步测试验证。
     *
     * @param context
     */
    public static void clear(Context context) {
        if (mPreference == null) {
            mPreference = PreferenceManager.getDefaultSharedPreferences(context);
        }
        SharedPreferences.Editor editor = mPreference.edit();
        editor.clear();
        editor.commit();
    }
}
