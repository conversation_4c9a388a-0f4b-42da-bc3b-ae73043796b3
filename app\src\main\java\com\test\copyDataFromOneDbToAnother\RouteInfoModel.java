package com.test.copyDataFromOneDbToAnother;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @todo 路由器的mac地址，网关IP，路由品牌.
 * @date 2014年5月5日 下午3:14:31
 */
public class RouteInfoModel implements Serializable {
	private static final long serialVersionUID = 2351937143267456172L;
	// mac地址
	private String mac;
	// 网关IP
	private String gateIP;
	private String brand;

	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public String getMac() {
		return mac;
	}

	public void setMac(String mac) {
		this.mac = mac;
	}

	public String getGateIP() {
		return gateIP;
	}

	public void setGateIP(String gateIP) {
		this.gateIP = gateIP;
	}

}
