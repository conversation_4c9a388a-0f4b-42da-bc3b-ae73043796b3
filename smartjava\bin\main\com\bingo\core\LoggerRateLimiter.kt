package com.bingo.core

import java.util.concurrent.ConcurrentHashMap

/**
 * 限制每个方法，每秒输出日志的数量
 */
object LoggerRateLimiter {
    /**
     * 方法签名作为key值，比如 SyncPhoneFilesKt.main
     * 每个方法输出日志的时间戳，作为value值
     */
    val timestamps = ConcurrentHashMap<String, Long>()

    inline fun logIfAllowed(crossinline message: () -> String) {
        try {
            val stackTrace = Thread.currentThread().stackTrace
            // 根据调试结果调整索引（此处示例为通用场景）
            val caller = stackTrace[2]
            val className = caller.className.substringAfterLast('.')
            val methodName = caller.methodName
            val key = "$className.$methodName"
            // 比如 MoveDirStrucAndPhoneFilesKt.doMoveDirStrucFiles,RenamePhoneFileAIKt.doRenamePhoneFileAI,MoveDirStrucAndPhoneFilesKt.doMovePhoneFiles

            val now = System.currentTimeMillis()
            val lastTimestamp = timestamps[key]

            if (lastTimestamp == null || now - lastTimestamp >= 389) {
                // 记录每个方法输出日志的时间戳
                timestamps[key] = now
                // println("[$key] ${message()}")
                println(message())
            }
        } catch (e: Exception) {
            // 防止日志逻辑崩溃主程序
            println("日志限流器异常: ${e.message}")
        }
    }
}