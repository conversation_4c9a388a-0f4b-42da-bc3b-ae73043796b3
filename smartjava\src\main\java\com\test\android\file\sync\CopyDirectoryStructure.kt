package com.test.android.file.sync

import java.io.File

fun main() {
    doCopyDirStruc()
}

/**
 * 请写出kotlin代码，实现如下功能：
 * 遍历文件夹 H:\Family\Phone 的所有文件，然后在 H:\Family\PhoneCopy 文件夹，
 * 创建 H:\Family\Phone 文件夹的所有同名子目录与子文件，创建的子文件为空文件。
 */
fun doCopyDirStruc() {
    val sourceDirFile = File(getSourcePath()) // 目标文件夹路径
    if (!sourceDirFile.exists() || !sourceDirFile.isDirectory) {
        sourceDirFile.mkdirs()
    }
    // val targetPath = File(getSourcePath()).parentFile.path + "\\" + File(getSourcePath()).name.substring(0, File(getSourcePath()).name.length / 2) + "_Struc"
    val targetPath = File(getSourcePath()).parentFile.path + "\\" + File(getSourcePath()).name + "StrucBC"
    val targetDirFile = File(targetPath)
    // 创建目标文件夹（如果不存在）
    if (!targetDirFile.exists() || !targetDirFile.isDirectory) {
        targetDirFile.mkdirs()
    }
    var fileMap = mutableMapOf<String, Boolean>()
    // 遍历源文件夹
    copyDirStruc(sourceDirFile, targetDirFile, fileMap)
}


fun copyDirStruc(source: File, target: File, fileMap: MutableMap<String, Boolean>) {
    // 遍历源文件夹中的所有文件和子目录
    source.listFiles()?.forEach { file ->
        val targetFile = File(target, file.name) // 创建目标文件的路径
        if (file.isDirectory) { // 如果是目录，则递归调用
            targetFile.mkdirs() // 创建同名目录
            copyDirStruc(file, targetFile, fileMap) // 递归复制子目录
        } else { // 如果是文件，则创建一个空文件
            // 判断文件名是否以任一用户名作为后缀
            if (getAllUserNames().any { file.nameWithoutExtension.endsWith("_$it") }) {
                return@forEach
            }
            fileMap.put(file.path, false)
            if (targetFile.createNewFile()) { // 创建空文件
                fileMap.put(file.path, true)
            }
        }
    }
    val trueCount = fileMap.values.count { it }
    val falseCount = fileMap.size - trueCount
    println("======copyDirStruc ---> ${target.path}  Success:$trueCount,Fail:$falseCount======\n")
}
