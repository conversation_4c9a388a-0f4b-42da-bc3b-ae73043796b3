package com.test.android.file;

import com.alibaba.fastjson.JSON;
import com.test.android.bean.FileInfoModel;
import com.test.android.common.Utils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * if one file in NEW_DIR_PATH is different or unique with files in OLD_DIR_PATH,then dirCopy the file to diff dir.
 */
public class FindDiffUniqueFile {
    private static final String OLD_DIR_PATH = "H:\\AutoSmart\\jq";
    private static final String NEW_DIR_PATH = "G:\\myapk\\jq";
    private static final String DIFF_FILE_INFO_TXT_PATH = "E:\\diff_file_info6.txt";//the format of txt file must be UTF-8 no BOM.
    private static final String DIFF_FILE_DIR = "G:\\myapk\\jq_temp_diff";

    /*private static final String OLD_DIR_PATH = "G:\\myapk\\jq_temp_diff";
    private static final String NEW_DIR_PATH = "G:\\myapk\\jq";
    private static final String DIFF_FILE_INFO_TXT_PATH = "E:\\test_test.txt";
    private static final String DIFF_FILE_DIR = "G:\\myapk\\diff";*/

    public static void main(String args[]) throws Exception {
        saveDirFileInfo2Txt(OLD_DIR_PATH, DIFF_FILE_INFO_TXT_PATH);
        delayToClosePC();

        //parseTxt2FindDiff(DIFF_FILE_INFO_TXT_PATH);
    }

    /**
     * @todo close the computer
     * <AUTHOR>
     * @time 2017/9/9 23:53
     */
    private static void delayToClosePC() {
        try {
            Thread.sleep(65000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        try {
            Runtime.getRuntime().exec("Shutdown -s");
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        System.out.println("close PC one");
        System.out.println();
    }


    private static void parseTxt2FindDiff(String txtPath) {
        String oldFileInfoString = Utils.readStringFromTxt(txtPath);
        List<FileInfoModel> oldModelListFromText = JSON.parseArray(oldFileInfoString, FileInfoModel.class);
        System.out.println("old dir total file count:" + oldModelListFromText.size());


        File newDirFile = new File(NEW_DIR_PATH);
        List<FileInfoModel> newDirFileInfo = recursiveDirFileInfo(newDirFile);
        System.out.println("new dir total file count:" + newDirFileInfo.size());

        List<FileInfoModel> sameFileList = new ArrayList<>();
        for (FileInfoModel newModel : newDirFileInfo) {
            String newShortPath = newModel.getFilePath().replace(NEW_DIR_PATH, "");
            long newFileLength = newModel.getFileLength();
            for (FileInfoModel oldModel : oldModelListFromText) {
                String oldShortPath = oldModel.getFilePath().replace(OLD_DIR_PATH, "");
                long oldFileLength = oldModel.getFileLength();
                if (oldShortPath.equalsIgnoreCase(newShortPath) && oldFileLength == newFileLength) {
                    //only find the same file
                    sameFileList.add(newModel);
                    System.out.println(oldShortPath);
                } /*else {
                    System.out.println("newPath:" + newShortPath);
                    System.out.println("oldPath:" + oldShortPath);
                    System.out.println("newLength:" + newFileLength);
                    System.out.println("oldLenth:" + oldFileLength);
                }*/
            }
        }
        //newDirFileInfo.removeAll(sameFileList);
        /*List<FileInfoModel> diffFileList = new ArrayList<>();
        for (FileInfoModel sameModel : sameFileList) {
            for (FileInfoModel newModel : newDirFileInfo) {
            }
        }*/
        System.out.println("diff files total count:" + (newDirFileInfo.size() - sameFileList.size()));
        //dirCopy the different and new file and directory.
        boolean isSameFile;
        for (FileInfoModel newModel : newDirFileInfo) {
            isSameFile = false;
            for (FileInfoModel sameModel : sameFileList) {
                if (newModel.getFileLength() == sameModel.getFileLength() && newModel.getFilePath().equalsIgnoreCase(sameModel.getFilePath())) {
                    isSameFile = true;
                    break;
                }
            }
            //except the same files,then we get the diff and new files.
            if (!isSameFile) {
                String newFilePath = newModel.getFilePath();
                System.out.println("dirCopy diff file:" + newFilePath);
                String diffPath = newFilePath.replace(NEW_DIR_PATH, DIFF_FILE_DIR);
                if (new File(newFilePath).isDirectory()) {
                    Utils.createEnableDir(diffPath);
                } else {
                    Utils.nioTransferCopyFile(new File(newFilePath), new File(diffPath));
                }
            }
        }
        System.out.println("all diff and new files have been copied!");
    }

    private static void saveDirFileInfo2Txt(String dirPath, String txtPath) {
        File dirFile = new File(dirPath);
        List<FileInfoModel> dirFileInfo = recursiveDirFileInfo(dirFile);
        System.out.println("total file count:" + dirFileInfo.size());

        String dirJson = JSON.toJSONString(dirFileInfo, true);
        System.out.println("dirJson:" + dirJson);
        Utils.writeString2Txt(dirJson, txtPath);
    }


    /**
     * recursive dir to find all files and directory in all subfolder.
     *
     * @param dirFile
     * @return
     */
    private static List<FileInfoModel> recursiveDirFileInfo(File dirFile) {
        List<FileInfoModel> modelList = new ArrayList<>();
        File[] listFiles = dirFile.listFiles();
        for (File item : listFiles) {
            FileInfoModel model = new FileInfoModel();
            if (item.isDirectory()) {
                model.setFilePath(item.getPath());
                model.setFileLength(0);
                modelList.add(model);
                modelList.addAll(recursiveDirFileInfo(item));
            } else {
                model.setFilePath(item.getPath());
                model.setFileLength(item.length());
                modelList.add(model);
                System.out.println("path:" + item.getPath());
                System.out.println("length:" + item.length());
            }
        }
        return modelList;
    }
}