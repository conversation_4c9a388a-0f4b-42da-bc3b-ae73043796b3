package com.test.android.jxbycg

import com.test.android.common.Number
import kotlin.math.ceil
import kotlin.math.floor

/**
 * 折边
 */
object EdgeFold {
    @JvmStatic
    fun main(args: Array<String>) {
        jiangZhu20240712()
        //jiangZhu20240730()
    }

    /**
     * 江竹实业老板的小蓝工地
     *
     * 注意：录入折边数据时，需要区分每个参数的单位。()
     */
    private fun jiangZhu20240712() {
        // 3.5表示折边的不含税加工费，除以0.9可以计算出含税的加工费
        val colorPlate = ColorPlate(1000, 28.3f, "BySquare", 3.5f / 0.9f)

        var sides = intArrayOf(25, 30, 150, 150, 30, 25)
        var edgeFoldItems = arrayOf(EdgeFoldItem(3.2f, 6), EdgeFoldItem(4.2f, 2), EdgeFoldItem(4.5f, 12))
        val edgeFoldModel1 = EdgeFoldModel(sides, edgeFoldItems)

        sides = intArrayOf(20, 20, 150, 70, 50)
        edgeFoldItems = arrayOf(EdgeFoldItem(6f, 20))
        val edgeFoldModel2 = EdgeFoldModel(sides, edgeFoldItems)

        sides = intArrayOf(20, 50, 50, 50)
        edgeFoldItems = arrayOf(EdgeFoldItem(3.15f, 19))
        val edgeFoldModel3 = EdgeFoldModel(sides, edgeFoldItems)

        sides = intArrayOf(20, 50, 50, 150)
        edgeFoldItems = arrayOf(EdgeFoldItem(3.15f, 19))
        val edgeFoldModel4 = EdgeFoldModel(sides, edgeFoldItems)

        sides = intArrayOf(20, 50, 50, 150)
        edgeFoldItems = arrayOf(EdgeFoldItem(1.9f, 38))
        val edgeFoldModel5 = EdgeFoldModel(sides, edgeFoldItems)

        sides = intArrayOf(20, 60, 50, 50)
        edgeFoldItems = arrayOf(EdgeFoldItem(164f)) // 表示总长为164米，不限制单块的长度
        val edgeFoldModel6 = EdgeFoldModel(sides, edgeFoldItems)

        sides = intArrayOf(30, 30, 30, 210, 50)
        edgeFoldItems = arrayOf(EdgeFoldItem(27f)) // 表示总长为27米，不限制单块的长度
        val edgeFoldModel7 = EdgeFoldModel(sides, edgeFoldItems)

        val edgeFoldList = arrayOf(edgeFoldModel1, edgeFoldModel2, edgeFoldModel3, edgeFoldModel4, edgeFoldModel5, edgeFoldModel6, edgeFoldModel7)
        quotationList(edgeFoldList, colorPlate)
        blankingList(edgeFoldList, colorPlate)
    }

    /**
     * 江竹实业老板的小蓝工地
     *
     * 注意：录入折边数据时，需要区分每个参数的单位。
     */
    private fun jiangZhu20240730() {
        // 10表示折边的不含税加工费，除以0.9可以计算出含税的加工费
        val colorPlate = ColorPlate(1000, 22f, "FixedPrice", 10f / 0.9f)

        var sides = intArrayOf(30, 30, 150, 150, 30, 30)
        var edgeFoldItems = arrayOf(EdgeFoldItem(2.2f, 2))
        val edgeFoldModel1 = EdgeFoldModel(sides, edgeFoldItems)
        val edgeFoldList = arrayOf(edgeFoldModel1)
        quotationList(edgeFoldList, colorPlate)
        blankingList(edgeFoldList, colorPlate)
    }


    /**
     * 报价单
     *
     * 参考：折边报价单.jpg
     *
     * 20240825_135936_wx_camera.jpg
     * 沟通怎么下料
     * 20240825_140629_微信录音 英英.aac
     *
     */
    private fun quotationList(edgeFoldModels: Array<EdgeFoldModel>, colorPlate: ColorPlate) {
        println()
        println("------------报价单------------")
        var totalCost = 0
        for (i in edgeFoldModels.indices) {
            println()
            println("---折边图" + (i + 1) + "---")
            println(edgeFoldModels[i].sides.contentToString())
            val edgeFoldWidth = edgeFoldModels[i].width
            println("折边宽度：" + edgeFoldWidth + "毫米")
            val items = edgeFoldModels[i].edgeFoldItems

            /**
             * 折边的总长度
             */
            var edgeFoldSum = 0f

            /**
             * 需要下料的彩钢板总长度
             */
            var blankingSum = 0f

            /**
             * //每张1m或1.2m宽彩钢板可以制作折边的块数
             */
            val perPieces = floor(colorPlate.colorPlateWidth.toDouble() / edgeFoldWidth).toInt()
            // System.out.println(per_pieces);
            for (item in items) {
                edgeFoldSum += item.totalEdgeFoldLength
                if (item.edgeFoldLength > 0) {
                    //当对每块折边的长度有要求时
                    println(item.edgeFoldLength.toString() + "米/块 * " + item.edgeFoldAmount + "块 = " + Number.formatDouble((item.edgeFoldLength * item.edgeFoldAmount).toDouble()) + "米")
                    val blankingAmount = ceil(item.edgeFoldAmount.toDouble() / perPieces).toInt() //需要下料彩钢板的数量
                    // System.out.println(blanking_amount);
                    // item.needBlankingLength = item.length
                    //item.needBlankingAmount = blankingAmount
                    blankingSum += item.edgeFoldLength * blankingAmount
                } else { //有些型号的折边对每块的长度没有要求，只要求总长度
                    blankingSum += item.totalEdgeFoldLength / perPieces
                }
            }

            /**
             * 折边的单价
             */
            var edgeFoldPrice = 0.0f

            if (colorPlate.processingChargeType == "BySquare") {
                //每米折边的单价=需要下料的彩钢板总长度*彩钢板的含税含加工费单价/折边的总长度
                edgeFoldPrice = blankingSum * (colorPlate.colorPlatePriceWithTax + colorPlate.processingChargeWithTax) / edgeFoldSum
            } else if (colorPlate.processingChargeType == "FixedPrice") {
                //每米折边的单价=(需要下料的彩钢板总长度*彩钢板的含税单价+一口价加工费)/折边的总长度
                edgeFoldPrice = (blankingSum * colorPlate.colorPlatePriceWithTax + colorPlate.processingChargeWithTax) / edgeFoldSum
            }

            if (items.size > 1) {
                println("------------------")
                println("总长度：" + Number.formatDouble(edgeFoldSum.toDouble()) + "米 * " + Number.formatDouble(edgeFoldPrice.toDouble()) + "元/米 = " + Number.formatDouble(floor((edgeFoldSum * edgeFoldPrice).toDouble())) + "元")
                totalCost += (floor((edgeFoldSum * edgeFoldPrice).toDouble())).toInt()
            } else {
                println(Number.formatDouble(edgeFoldSum.toDouble()) + "米 * " + Number.formatDouble(edgeFoldPrice.toDouble()) + "元/米 = " + Number.formatDouble(floor((edgeFoldSum * edgeFoldPrice).toDouble())) + "元")
                totalCost += (floor((edgeFoldSum * edgeFoldPrice).toDouble())).toInt()
            }
        }
        println("========================")
        println("总金额：$totalCost" + "元")
    }

    /**
     * 下料单
     *
     * 参考：折边下料单.jpg
     *
     * 20240825_135936_wx_camera.jpg
     * 沟通怎么下料
     * 20240825_140629_微信录音 英英.aac
     *
     */
    private fun blankingList(edgeFoldList: Array<EdgeFoldModel>, colorPlate: ColorPlate) {
        var sortedEdgeFoldList: List<EdgeFoldModel> = edgeFoldList.sortedByDescending { it.width } // sortedByDescending 降序排列
        // BingoTodo 对这个排序规则，需要再优化一下：如果两种折边的宽度相同，长度更长的优先下料; sortedByDescending 这个排序方法，是否可以设置两个排序变量呢？
        //println()
        //sortedEdgeFoldList.forEach { println(it.width) }
        println()
        println("------------下料单------------")
        for (i in sortedEdgeFoldList.indices) {
            println()
            println(sortedEdgeFoldList[i].sides.contentToString())
            /**
             * 折边宽度
             */
            var edgeFoldWidth = sortedEdgeFoldList[i].width
            println("折边宽度：" + edgeFoldWidth + "毫米")
            val items = sortedEdgeFoldList[i].edgeFoldItems

            /**
             * 折边的总长度
             */
            var edgeFoldSum = 0f

            /**
             * 需要下料的彩钢板总长度
             */
            //var blankingSum = 0f

            /**
             * 每张1m或1.2m宽彩钢板可以制作折边的块数
             */
            val perPieces = floor(colorPlate.colorPlateWidth.toDouble() / edgeFoldWidth).toInt()
            // System.out.println(per_pieces);
            for (item in items) {
                edgeFoldSum += item.totalEdgeFoldLength
                if (item.edgeFoldLength == 0f) {
                    //有些型号的折边对每块的长度没有要求，只要求总长度
                    continue
                }
                //当对每块折边的长度有要求时
                println(item.edgeFoldLength.toString() + "米/块 * " + item.edgeFoldAmount + "块 = " + Number.formatDouble((item.edgeFoldLength * item.edgeFoldAmount).toDouble()) + "米")
                /**
                 * 需要下料彩钢板的数量
                 */
                val blankingAmount = ceil(item.edgeFoldAmount.toDouble() / perPieces).toInt()
                // 注意：当用户需要的折边块数，比1块下料的彩钢板可以裁剪的折边数量都要少时，blankingAmount=1
                println("下料：$blankingAmount" + "块")
                // item.needBlankingLength = item.length
                //item.needBlankingAmount = blankingAmount
                //blankingSum += item.length * blankingAmount

                item.blankingEdgeFoldAmount = item.edgeFoldAmount
                item.blankingColorPlateModel.blankingCPWidth = colorPlate.colorPlateWidth
                item.blankingColorPlateModel.blankingCPLength = item.edgeFoldLength
                item.blankingColorPlateModel.blankingCPAmount = blankingAmount
                item.blankingColorPlateModel.edgeFoldInfos.add(EdgeFoldInfo(item.edgeFoldLength, edgeFoldWidth.toFloat(), item.edgeFoldAmount))

                /**
                 * 彩钢板裁剪N块折边后多余的宽度
                 */
                var extraWidth1 = colorPlate.colorPlateWidth - edgeFoldWidth * perPieces
                if (item.edgeFoldAmount % perPieces == 0) { // 需要下料彩钢板的数量与折边的数量，刚好成比例
                    item.blankingColorPlateModel.extraColorPlates.add(ExtraColorPlate(item.edgeFoldLength, extraWidth1.toFloat(), blankingAmount))
                } else {
                    // 注意：当用户需要的折边块数，比1块下料的彩钢板可以裁剪的折边数量都要少时，item.edgeFoldAmount % perPieces > 0，但是 blankingAmount=1
                    if (blankingAmount > 1) {
                        item.blankingColorPlateModel.extraColorPlates.add(ExtraColorPlate(item.edgeFoldLength, extraWidth1.toFloat(), blankingAmount - 1))
                    }
                    // 最后一块彩钢板需要裁剪的折边数量
                    val lastEdgeFoldAmount = item.edgeFoldAmount - perPieces * (blankingAmount - 1)
                    // 最后一块彩钢板裁剪lastEdgeFoldAmount块折边后多余的宽度
                    var extraWidth2 = colorPlate.colorPlateWidth - edgeFoldWidth * lastEdgeFoldAmount
                    item.blankingColorPlateModel.extraColorPlates.add(ExtraColorPlate(item.edgeFoldLength, extraWidth2.toFloat(), 1))
                }

                //余料复用
                //reuseExtra(sortedEdgeFoldList,i)
                for (j in sortedEdgeFoldList.indices) {
                    if (j > i) { // 因为sortedEdgeFoldList已经按照折边宽度降序排列，所以需要从下一个型号的折边开始复用余料
                        for (extraColorPlate in item.blankingColorPlateModel.extraColorPlates) {
                            if (sortedEdgeFoldList[j].width <= extraColorPlate.width) {
                                for (edgeFoldItem in sortedEdgeFoldList[j].edgeFoldItems) {
                                    if (edgeFoldItem.edgeFoldLength == 0.0f) {
                                        //有些型号的折边对每块的长度没有要求，只要求总长度
                                        //那么，在复用余料的时候，按照余料的长度加工折边
                                        if (edgeFoldItem.totalEdgeFoldLength >= (extraColorPlate.length * extraColorPlate.amount)) {
                                            edgeFoldItem.blankingTotalEdgeFoldLength = extraColorPlate.length * extraColorPlate.amount
                                            item.blankingColorPlateModel.extraColorPlates.remove(extraColorPlate)
                                            item.blankingColorPlateModel.edgeFoldInfos.add(EdgeFoldInfo(extraColorPlate.length, sortedEdgeFoldList[j].width.toFloat(), extraColorPlate.amount))
                                            if (sortedEdgeFoldList[j].width < extraColorPlate.width) {
                                                //复用一次之后，还有宽度的富余，需要递归调用reuseExtra方法，继续复用余料
                                            }
                                        } else {

                                        }
                                    } else {

                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        //最先预下料最宽折边，再计算余料是否可以满足次最宽折边的要求，依次类推，如果余料比最窄的折边还窄，则不能再复用。余料只循环复用一次，如果复用次数太多，会增加加工的难度，也不能冗余加工的失误。

        //为什么最先预下料最宽折边？
        //因为最宽的折边，较难被其他折边的余料补充；

        //如何判断余料是否可用？
        //需要余料的宽度更宽，长度更长

        //如果有些型号的折边，没有要求每块折边的长度，只要求了折边的总长度。应该按照什么顺序对这种折边进行下料？
        //没有写明长度的折边，每块按多少长度加工呢？3米？不能太长，否则运输成本太高。

        //如果折边A的单根长度没有限制，则以余料可以满足的最宽折边B的长度为准，如果折边B的数量小于折边A，则折边A先按照折边B的长度和数量进行裁剪，剩余数量的折边A再按照余料可以满足的次最宽折边C的长度进行裁剪。
        //裁剪完成之后，需要验证：余料的总面积+折边的总面积=下料彩钢板的总面积
        //按折边宽度排序后的数组与排序之前的数组（用户提供的图纸）进行一一对应时，要求  sides的数组大小  width  length  amount  totalLength 均一致
    }

    private fun reuseExtra(sortedEdgeFoldList: List<EdgeFoldModel>, i: Int) {

    }
}
