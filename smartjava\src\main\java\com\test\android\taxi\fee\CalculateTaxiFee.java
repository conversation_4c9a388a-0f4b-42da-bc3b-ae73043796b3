package com.test.android.taxi.fee;

import com.test.android.SomeUtils;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @todo 通过日志的形式，输出出租车打车票的相关信息。
 */
public class CalculateTaxiFee {
    public static void main(String[] args) {
        String on_car_times[] = {"2016-12-01 21:43", "2016-12-14 22:35", "2016-12-15 22:31", "2016-12-20 22:33", "2016-12-21 22:05",
                "2016-12-22 22:19", "2016-12-28 21:55"};
        /*String on_car_times[] = { "2016-11-01 22:17", "2016-11-02 22:21", "2016-11-04 22:18", "2016-11-08 22:26", "2016-11-09
        22:11",
				"2016-11-10 21:16", "2016-11-17 22:23", "2016-11-19 08:37", "2016-11-19 21:31", "2016-11-22 22:32", "2016-11-28
				22:13",
				"2016-11-29 22:11" };
		String on_car_times[] = { "2016-11-01 22:21", "2016-11-02 21:55", "2016-11-04 21:53", "2016-11-08 21:58", "2016-11-09 22:15",
				"2016-11-10 22:10", "2016-11-17 22:23", "2016-11-19 9:21", "2016-11-19 19:31", "2016-11-22 22:05" };*/
        /*String on_car_times[] = { "2016-10-08 21:51", "2016-10-09 22:35", "2016-10-10 21:55", "2016-10-11 21:56", "2016-10-12
        21:55",
				"2016-10-13 21:38", "2016-10-19 21:55", "2016-10-20 21:57", "2016-10-24 22:07", "2016-10-25 22:08", "2016-10-31
				22:09" };*/
		/*String on_car_times[] = { "2016-09-05 22:52", "2016-09-06 22:25", "2016-09-08 22:11", "2016-09-19 22:00", "2016-09-20
		22:17",
				"2016-09-21 22:30", "2016-09-22 22:11", "2016-09-23 21:55", "2016-09-26 22:25", "2016-09-27 23:18", "2016-09-28
				22:35",
				"2016-09-29 22:45" };*/

        final float REAL_DISTANCE = 16f;

        for (String on_car_time : on_car_times) {

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date date = null;
            try {
                date = simpleDateFormat.parse(on_car_time);
            } catch (ParseException e) {
                e.printStackTrace();
            }

            int hour = date.getHours();
            int minute = date.getMinutes();
            float DISTANCE = (float) (Math.random() * 3.0f + REAL_DISTANCE);
            int WAIT_SECOND_TIME = (int) (Math.random() * 6 * 60 + 65);// 等候时间，从2分钟到8分钟随机
            float drive_time = (float) (Math.random() * 10.0f + 23.0f + WAIT_SECOND_TIME / 60.0f);// 单位分钟
            // System.out.println("drive_time:" + drive_time);
            date.setTime((long) (date.getTime() + drive_time * 60 * 1000));

            float[] feePrice = totalTaxiFeePrice(hour, minute, DISTANCE, WAIT_SECOND_TIME);
            int monthInt = date.getMonth() + 1;
            int dateInt = date.getDate();
            DecimalFormat decimalFormat = new DecimalFormat("0.0");// 保留一位小数
            System.out.println("日期：" + (date.getYear() + 1900) + "年" + SomeUtils.convertSingleNumber(monthInt) + "月"
                    + SomeUtils.convertSingleNumber(dateInt) + "日");
            System.out.println("上车：" + SomeUtils.convertSingleNumber(hour) + ":" + SomeUtils.convertSingleNumber(minute));
            System.out.println("下车：" + SomeUtils.convertSingleNumber(date.getHours()) + ":" + SomeUtils.convertSingleNumber(date
                    .getMinutes()));
            DecimalFormat decimalFormat2 = new DecimalFormat("0.00");// 保留一位小数
            System.out.println("单价：" + decimalFormat2.format(feePrice[1]) + "元");
            System.out.println("里程：" + decimalFormat.format(DISTANCE) + "km");
            System.out.println("等候：" + "00:0" + WAIT_SECOND_TIME / 60 + ":" + SomeUtils.convertSingleNumber(WAIT_SECOND_TIME % 60));
            System.out.println("金额：￥" + decimalFormat.format(feePrice[0]) + "0元");
            System.out.println("-------------------------------------");
        }
    }

    private static float[] totalTaxiFeePrice(int on_car_hour, int on_card_minute, float distance, int wait_second_time) {
        // 起步价为10元/2公里，之后每1公里/2.4元，停车等候时每分钟/0.8元，
        // 夜间起步价13元(23时―6时)，按30%加收夜间附加费。
        float total_fee;
        float first_road_fee = 10;
        float after_road_price = 2.4f;
        float wait_time_price = 0.8f;
        if (on_car_hour >= 23 || on_car_hour <= 6) {
            first_road_fee = first_road_fee * 1.3f;
            after_road_price = after_road_price * 1.3f;
            wait_time_price = wait_time_price * 1.3f;
        }
        // 计算路程产生的费用
        if (distance <= 2) {
            total_fee = first_road_fee;
        } else {
            total_fee = first_road_fee + (distance - 2.0f) * after_road_price;
        }
        // System.out.println("roat_fee:" + total_fee);

        // 每天6时至23时，超过25公里部分按里程价30%加收返空费。
        if (on_car_hour >= 6 && on_car_hour <= 23 && distance > 25) {
            float long_distance_fee = (distance - 25) * after_road_price * 0.3f;
            // System.out.println("long_distance_fee:" + long_distance_fee);
            total_fee += long_distance_fee;
        }

        // 计算等候产生的费用
        double wait_fee = Math.ceil(wait_second_time / 60.0f) * wait_time_price;
        // System.out.println("wait_fee:" + wait_fee);
        total_fee += wait_fee;

        // System.out.println("---total_fee---:" + total_fee);
        return new float[]{total_fee, after_road_price};
    }

}
