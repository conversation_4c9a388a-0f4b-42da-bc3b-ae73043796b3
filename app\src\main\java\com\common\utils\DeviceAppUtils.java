package com.common.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.telephony.TelephonyManager;

import java.lang.reflect.Method;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class DeviceAppUtils {
    public static String APP_PKG_NAME = null;
    //有版本编码就可以了
    public static int APP_VER_CODE = 0;

    public static String md5Appkey(String str) throws NoSuchAlgorithmException {
        try {
            MessageDigest localMessageDigest = MessageDigest.getInstance("MD5");
            //原来的代码，但是出现报错
            //localMessageDigest.update(getBytes(str));

            //我自己修改正后的
            localMessageDigest.update(str.getBytes());
            byte[] arrayOfByte = localMessageDigest.digest();
            StringBuffer localStringBuffer = new StringBuffer(64);
            for (int i = 0; i < arrayOfByte.length; i++) {
                int j = 0xFF & arrayOfByte[i];
                if (j < 16)
                    localStringBuffer.append("0");
                localStringBuffer.append(Integer.toHexString(j));
            }
            return localStringBuffer.toString();
        } catch (NoSuchAlgorithmException e) {
        }
        return "";
    }

    /**
     * 获取设备的序列号
     *
     * @return
     */
    private static String getDeviceSerial() {
        String serial = null;
        try {
            Class<?> c = Class.forName("android.os.SystemProperties");
            Method get = c.getMethod("get", String.class);
            serial = (String) get.invoke(c, "ro.serialno");
        } catch (Exception e) {
            //            LivingLog.d("jialiwei-hj", "getDeviceSerial: ");(TAG, "", e);
        }
        return serial;
    }

    /**
     * 计算设备的md2值.
     *
     * @param context
     * @return
     */
    public static String getM2(Context context) {
        String sImei2;
        try {
            final TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            String imei = tm.getDeviceId();
            //此处调用系统函数获取ANDROID_ID，在设备首次启动时，系统会随机生成一个64位的数字，并把这个数字以16进制字符串的形式保存下来，这个16进制的字符串就是ANDROID_ID，当设备被wipe后该值会被重置
            String androidId = android.provider.Settings.System.getString(context.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);
            //此处计算serialNo，serialNo适合操作系统相关一个参数，具体计算方法请参考下面的getDeviceSerial函数
            String serialNo = getDeviceSerial();
            //此处计算m2，下面附带有MD5Util类供参考
            sImei2 = md5Appkey("" + imei + androidId + serialNo);
            return sImei2;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 初始化应用的版本号、版本名称信息，便于后续的统计与问题的修复。
     *
     * @param context
     */
    public static void logAPPVersionInfo(Context context) {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo pinfo = pm.getPackageInfo(context.getPackageName(), PackageManager.GET_CONFIGURATIONS);
            //APP_VER_NAME = pinfo.versionName;
            APP_VER_CODE = pinfo.versionCode;
            Logs.i("APP_VER_CODE:" + APP_VER_CODE);
            APP_PKG_NAME = pinfo.packageName;
            Logs.i("APP_PKG_NAME:" + APP_PKG_NAME);
        } catch (Exception e) {
        }
    }
}
