package com.common.utils;

import com.google.gson.Gson;
import com.google.gson.JsonParseException;

public class ToFromJson<T> {
	/**
	 * 把对象，转换为JSON字符串
	 * 
	 * 2013-5-19 下午9:19:11
	 */
	public String toJson(T t) {
		return new Gson().toJson(t);
	}

	/**
	 * 把JSON字符串，转换为指定的对象
	 * 
	 * 2013-5-19 下午9:19:45
	 */
	public T fromJson(String json, Class<T> c) {
		try {
			return new Gson().fromJson(json, c);
		} catch (JsonParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	// 转换为对象List的示例代码
	/*Gson gson=new Gson();
	List<CityCodeModel> cityCodeModels = gson.fromJson(fromServerJson, new TypeToken<List<CityCodeModel>>() {
	}.getType());*/

	/**
	 * 把JSON字符串，转换为指定的对象数组
	 * 
	 * 2013-5-19 下午9:20:23
	 */
	/*public ArrayList<CityCodeModel> toCityCodeModelListFromJsonArray(String json, Class<T> c) {
		try {
			Type listType = new TypeToken<ArrayList<CityCodeModel>>() {
			}.getType();
			ArrayList<CityCodeModel> blacklist = new Gson().fromJson(json, listType);
			if (!blacklist.isEmpty() && blacklist.size() > 0) {
				return blacklist;
			}
		} catch (JsonParseException e) {
			e.printStackTrace();
		}
		return null;
	}*/

}
