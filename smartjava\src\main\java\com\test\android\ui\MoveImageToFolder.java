package com.test.android.ui;

import java.awt.BorderLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;

class MainUI extends JFrame implements Runnable, ActionListener {
    private static final long serialVersionUID = 1L;
    private final JFrame frame = new JFrame("图片分类");
    private final String[] typeNames = {"医院", "公司", "大学", "中心"};
    private final JPanel inputPanel = new JPanel();
    private final JPanel btnPanel = new JPanel();
    final JTextField imagePathField = new JTextField();

    private Thread startThread;// 声明一个线程

    public MainUI() {
        JLabel imagePathLabel = new JLabel("存放图片的文件夹路径：");
        startThread = new Thread(this);// 初始化进程
        inputPanel.add(imagePathLabel);

        imagePathField.setColumns(35);
        inputPanel.add(imagePathField);

        frame.add(inputPanel, BorderLayout.NORTH);
        JButton actionButton = new JButton("开始分类");
        btnPanel.add(actionButton);
        frame.add(btnPanel, BorderLayout.CENTER);
        frame.setBounds(380, 120, 600, 120);
        frame.setVisible(true);
        frame.validate();
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        addWindowListener(new WindowAdapter() {
            public void windowClosing(WindowEvent e) {
                // super.windowClosing(e);
                System.exit(0);
            }
        });
        actionButton.addActionListener(this);
    }



    private void recurseMoveFileToDirectory(File imageParentFile, File file) {
        File[] listFiles = file.listFiles();
        if (listFiles != null && listFiles.length > 0) {
            for (File itemFile : listFiles) {
                // 遍历所有的图片
                if (itemFile.isDirectory()) {
                    //如果是目录的话，则需要循环遍历。
                    recurseMoveFileToDirectory(imageParentFile, itemFile);
                } else {
                    moveFileToDirectory(imageParentFile, itemFile);
                }
            }
        }
    }

    private void moveFileToDirectory(File imageParentFile, File file) {
        String fileName = file.getName();
        if (fileName.endsWith(".png") || fileName.endsWith(".jpg")) {
            for (String typeItem : typeNames) {
                if (fileName.contains(typeItem)) {
                    int lastIndexOf = fileName.lastIndexOf(typeItem);
                    String imageTypeName = fileName.substring(0, lastIndexOf);
                    // 截取图片中，含有公司、大学字段的部分
                    System.out.println("imageItemFolderName:" + imageTypeName + typeItem);
                    String imageItemFolderString = imageParentFile.getAbsolutePath() + "\\" + imageTypeName + typeItem;
                    File imageTypeFolder = new File(imageItemFolderString);
                    if (!imageTypeFolder.exists()) {
                        // 创建含有公司、大学字段名称的文件夹
                        imageTypeFolder.mkdirs();
                    }
                    try {
                        File destFile = new File(imageItemFolderString + "\\" + fileName);
                        if (!destFile.exists()) {
                            Files.move(file.toPath(), destFile.toPath());
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    break;
                }
            }
        }
    }

    public void actionPerformed(ActionEvent e) {
        // startButton的响应事件
        if (!startThread.isAlive()) { // 决定是否创建进程
            startThread = new Thread(this);
        }
        try {
            startThread.start(); // 启动进程
        } catch (Exception exp) { // 捕获异常
        }
    }

    public void run() {
        String imageFolderPath = imagePathField.getText().toString().trim();
        if (imageFolderPath == null || "".equals(imageFolderPath)) {
            JOptionPane.showMessageDialog(frame, "存放图片的文件夹路径，不能为空！"); // 弹出提示窗口
            return;
        }
        File imageFolderFile = new File(imageFolderPath);
        File imageParentFile = imageFolderFile.getParentFile();
        if (imageFolderFile.isDirectory() && imageFolderFile.exists()) {
            File[] listFiles = imageFolderFile.listFiles();
            // 如果原来的图片目录中，含有图片
            if (listFiles != null && listFiles.length > 0) {
                for (File file : listFiles) {
                    // 遍历所有的图片
                    if (file.isDirectory()) {
                        //如果是目录的话，则需要循环遍历。
                        recurseMoveFileToDirectory(imageParentFile, file);
                    } else {
                        moveFileToDirectory(imageParentFile, file);
                    }
                }
                JOptionPane.showMessageDialog(frame, "图片分类完成，请查看文件夹：" + imageParentFile.getAbsolutePath());
            } else {
                JOptionPane.showMessageDialog(frame, "文件夹内没有图片");
            }
        } else {
            JOptionPane.showMessageDialog(frame, "文件夹路径输入有误");
        }
    }

}

public class MoveImageToFolder {
    /**
     * @param args
     * @todo TODO
     * <AUTHOR>
     * @date 2018-5-25 上午1:09:39
     */
    public static void main(String[] args) {
        new MainUI();
    }
}
