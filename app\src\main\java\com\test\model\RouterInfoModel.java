package com.test.model;

/**
 * <AUTHOR>
 * @todo
 * @date 2014年9月24日 下午2:45:28
 */
public class RouterInfoModel {
	String Dns1;
	String Dns2;
	String Gateway;
	String IpAddress;
	String Netmask;
	int LeaseDuration;
	String Bssid;
	boolean Hiddenssid;
	String Ip;
	int Linkspeed;
	String Mac;
	int Networkid;
	int Rssi;
	String Ssid;

	public String getDns1() {
		return Dns1;
	}

	public void setDns1(String dns1) {
		Dns1 = dns1;
	}

	public String getDns2() {
		return Dns2;
	}

	public void setDns2(String dns2) {
		Dns2 = dns2;
	}

	public String getGateway() {
		return Gateway;
	}

	public void setGateway(String gateway) {
		Gateway = gateway;
	}

	public String getIpAddress() {
		return IpAddress;
	}

	public void setIpAddress(String ipAddress) {
		IpAddress = ipAddress;
	}

	public String getNetmask() {
		return Netmask;
	}

	public void setNetmask(String netmask) {
		Netmask = netmask;
	}

	public int getLeaseDuration() {
		return LeaseDuration;
	}

	public void setLeaseDuration(int leaseDuration) {
		LeaseDuration = leaseDuration;
	}

	public String getBssid() {
		return Bssid;
	}

	public void setBssid(String bssid) {
		Bssid = bssid;
	}

	public boolean isHiddenssid() {
		return Hiddenssid;
	}

	public void setHiddenssid(boolean hiddenssid) {
		Hiddenssid = hiddenssid;
	}

	public String getIp() {
		return Ip;
	}

	public void setIp(String ip) {
		Ip = ip;
	}

	public int getLinkspeed() {
		return Linkspeed;
	}

	public void setLinkspeed(int linkspeed) {
		Linkspeed = linkspeed;
	}

	public String getMac() {
		return Mac;
	}

	public void setMac(String mac) {
		Mac = mac;
	}

	public int getNetworkid() {
		return Networkid;
	}

	public void setNetworkid(int networkid) {
		Networkid = networkid;
	}

	public int getRssi() {
		return Rssi;
	}

	public void setRssi(int rssi) {
		Rssi = rssi;
	}

	public String getSsid() {
		return Ssid;
	}

	public void setSsid(String ssid) {
		Ssid = ssid;
	}

	@Override
	public String toString() {
		return "RouterInfoModel [Dns1=" + Dns1 + ", Dns2=" + Dns2 + ", Gateway=" + Gateway + ", IpAddress=" + IpAddress + ", Netmask=" + Netmask + ", LeaseDuration=" + LeaseDuration + ", Bssid=" + Bssid + ", Hiddenssid="
		        + Hiddenssid + ", Ip=" + Ip + ", Linkspeed=" + Linkspeed + ", Mac=" + Mac + ", Networkid=" + Networkid + ", Rssi=" + Rssi + ", Ssid=" + Ssid + "]";
	}

}
