@echo off

::set github_src_folder=E:\develop\src\github_src\AIDemo\test
set github_src_folder=E:\develop\src\github_src
cd /d %github_src_folder%

setlocal enabledelayedexpansion
for /R %%s in (.,*) do (
		::echo %%s
		set path=%%s
		if exist !path! (
			set isFile=false
			for /f %%a in ('dir /b "!path!"') do (
					set tmp=!path:%%a=!
					if "!tmp!%%a"=="!path!" (
						::echo !path! is a file
						set isFile=true
					)
			)
			if "!isFile!"=="false" (
				:: 对延迟变量字符串进行截取，并赋值给其他变量
				set folder_name=!path:~-7!
				:: 对延迟变量字符串进行匹配
				if "!folder_name!"=="\.git\." (
					echo git_folder
					:: if path contain '_Bingo'
                    set n=!path:_Bingo=!
                    if !n!==!path! (
                        echo Not_Bingo_Folder
                        set git_parent_name=!path:~,-7!
                        echo !git_parent_name!
                        cd /d "!git_parent_name!"
                        echo ------execute_git_pull_--rebase------
                        E:\Software_installed\Git\bin\git.exe pull --rebase
                    ) else (
                        echo ======Delete_Bingo_Git_Folder======
                        echo !path!
                        rd /s /q !path!
                    )
				)
			)
		) else (
			echo !path! not exist.
		)
)