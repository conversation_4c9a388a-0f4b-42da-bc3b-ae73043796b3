package com.common.utils;

import android.util.Log;

import java.util.HashMap;
import java.util.Map;

/**
 * use for count the cost of time when run some method.
 * <p>
 * search key word:cost time,to find the log printed.
 */
public class StopWatch {
    private static Map<String, Long> tagTimeCache = new HashMap<>();

    public static void begin(String tag) {
        tagTimeCache.put(tag, System.currentTimeMillis());
    }

    public static void stop(String tag) {
        Long stopTime = tagTimeCache.get(tag);
        if (stopTime != 0) {
            Log.i(tag, " cost time:" + String.valueOf(System.currentTimeMillis() - stopTime));
        } else {
            Log.i(tag, "you should begin first.");
        }
    }
}
