package com.common.utils;

import android.os.StrictMode;

public class StrictModeWrapper {
    public static void initStrictMode() {
        StrictMode.ThreadPolicy threadPolicy = new StrictMode.ThreadPolicy.Builder()
                .detectCustomSlowCalls() //API等级11，使用StrictMode.noteSlowCode
                .detectDiskReads()
                .detectDiskWrites()
                .detectNetwork()   // or .detectAll() for all detectable problems.
                .penaltyLog()//write error info into log.
                .penaltyDialog()//popup dialog to show error info.
                .build();
        StrictMode.setThreadPolicy(threadPolicy);

        StrictMode.VmPolicy vmPolicy = new StrictMode.VmPolicy.Builder()
                .detectLeakedSqlLiteObjects()
                .detectLeakedClosableObjects()
                .penaltyLog()
                .penaltyDeath()//一旦StrictMode消息被写到LogCat后应用就会崩溃
                .build();
        StrictMode.setVmPolicy(vmPolicy);
    }
}
