package com.common.utils;


import android.content.Context;
import android.support.annotation.NonNull;
import android.util.TypedValue;

public class ScreenUtils {


    /**
     * 获取屏幕高度(px)
     *
     * @param context 需要用getApplicationContext，防止出现内存泄漏
     * @return
     */
    public static int getScreenHeight(Context context) {
        return context.getResources().getDisplayMetrics().heightPixels;
    }


    /**
     * 获取屏幕宽度(px)
     *
     * @param context 需要用getApplicationContext，防止出现内存泄漏
     * @return
     */
    public static int getScreenWidth(Context context) {
        return context.getResources().getDisplayMetrics().widthPixels;
    }

    /**
     * @param context 需要用getApplicationContext，防止出现内存泄漏
     * @param dp
     * @return
     */
    public static int dp2px(Context context, float dp) {
        return (int) (TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, context.getResources().getDisplayMetrics()) + 0.5f);
    }

    public static float getDensity(@NonNull Context context) {
        return 0f;
    }
}
