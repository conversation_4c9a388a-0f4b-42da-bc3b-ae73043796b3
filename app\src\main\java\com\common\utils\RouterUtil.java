package com.common.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.DhcpInfo;
import android.net.NetworkInfo.State;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.text.format.Formatter;

import com.test.model.RouterInfoModel;

/**
 * <AUTHOR>
 *         获取当前连接的WiFi的各类信息
 */
public class RouterUtil {
	public static RouterInfoModel getRouterInfo(Context context) {
		ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
		State wifi = manager.getNetworkInfo(ConnectivityManager.TYPE_WIFI).getState();
		if (wifi != State.CONNECTED) {
			return null;
		}
		WifiManager wifi_service = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);

		DhcpInfo dhcpInfo = wifi_service.getDhcpInfo();
		WifiInfo wifiinfo = wifi_service.getConnectionInfo();

		RouterInfoModel ri = new RouterInfoModel();
		String dns1 = Formatter.formatIpAddress(dhcpInfo.dns1);
		String dns2 = Formatter.formatIpAddress(dhcpInfo.dns2);
		String gateway = Formatter.formatIpAddress(dhcpInfo.gateway);
		String ipAddress = Formatter.formatIpAddress(dhcpInfo.ipAddress);
		String netmask = Formatter.formatIpAddress(dhcpInfo.netmask);
		int leaseDuration = dhcpInfo.leaseDuration;
		String bssid = wifiinfo.getBSSID();
		boolean hiddenssid = wifiinfo.getHiddenSSID();
		String ip = Formatter.formatIpAddress(wifiinfo.getIpAddress());
		int linkspeed = wifiinfo.getLinkSpeed();
		String mac = wifiinfo.getMacAddress();
		int networkid = wifiinfo.getNetworkId();
		int rssi = wifiinfo.getRssi();
		String ssid = wifiinfo.getSSID();

		ri.setDns1(dns1);
		ri.setDns2(dns2);
		ri.setGateway(gateway);
		ri.setIpAddress(ipAddress);
		ri.setNetmask(netmask);
		ri.setLeaseDuration(leaseDuration);
		ri.setBssid(bssid);
		ri.setHiddenssid(hiddenssid);
		ri.setIp(ip);
		ri.setLinkspeed(linkspeed);
		ri.setMac(mac);
		ri.setNetworkid(networkid);
		ri.setRssi(rssi);
		ri.setSsid(ssid);
		return ri;
	}
}