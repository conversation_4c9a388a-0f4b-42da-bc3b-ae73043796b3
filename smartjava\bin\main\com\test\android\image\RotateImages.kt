package com.test.android.image

import java.awt.Graphics2D
import java.awt.geom.AffineTransform
import java.awt.image.BufferedImage
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import javax.imageio.ImageIO

/**
 * 图像旋转工具类，可在 Windows (JVM) 上运行。
 * 提供 90, 180, 270 度的图像旋转功能，并覆盖原文件。
 */
object ImageRotatorKotlin {

    /**
     * 旋转 BufferedImage 图像。
     * 支持 90, 180, 270 度的精确旋转，避免插值模糊。
     *
     * @param originalImage 待旋转的原始图像。
     * @param degrees       旋转角度 (必须是 90, 180, 或 270)。
     * @return 旋转后的 BufferedImage，如果输入为空或角度无效则返回 null。
     */
    fun rotateImage(originalImage: BufferedImage?, degrees: Int): BufferedImage? {
        if (originalImage == null) {
            System.err.println("原始图像为空。")
            return null
        }

        // 角度归一化到 [0, 360) 范围
        val actualDegrees = (degrees % 360 + 360) % 360 // 确保结果为正且在 0-359 范围内

        // 0 度处理：返回原图的拷贝 (如果需要修改原图，0度也要重新写入一次)
        if (actualDegrees == 0) {
            val copy = BufferedImage(originalImage.width, originalImage.height, getImageType(originalImage))
            val g = copy.createGraphics()
            g.drawImage(originalImage, 0, 0, null)
            g.dispose()
            return copy
        }

        // 仅支持 90, 180, 270 度旋转
        if (actualDegrees != 90 && actualDegrees != 180 && actualDegrees != 270) {
            System.err.println("不支持的旋转角度: $actualDegrees 度。仅支持 90, 180, 270 度的精确旋转。")
            return null // 或者返回原图的拷贝
        }

        val originalWidth = originalImage.width
        val originalHeight = originalImage.height
        var newWidth = originalWidth
        var newHeight = originalHeight

        val transform = AffineTransform()

        when (actualDegrees) {
            90 -> {
                newWidth = originalHeight
                newHeight = originalWidth
                transform.translate(originalHeight.toDouble(), 0.0)
                transform.rotate(Math.toRadians(90.0))
            }

            180 -> {
                transform.translate(originalWidth.toDouble(), originalHeight.toDouble())
                transform.rotate(Math.toRadians(180.0))
            }

            270 -> {
                newWidth = originalHeight
                newHeight = originalWidth
                transform.translate(0.0, originalWidth.toDouble())
                transform.rotate(Math.toRadians(270.0))
            }
        }

        val rotatedImage = BufferedImage(newWidth, newHeight, getImageType(originalImage))

        val g2d: Graphics2D = rotatedImage.createGraphics()
        g2d.transform = transform
        g2d.drawImage(originalImage, 0, 0, null)
        g2d.dispose()

        return rotatedImage
    }

    /**
     * 辅助方法，用于获取 BufferedImage 的类型。
     */
    private fun getImageType(image: BufferedImage): Int {
        return if (image.transparency == BufferedImage.TRANSLUCENT) {
            BufferedImage.TYPE_INT_ARGB
        } else if (image.type == 0) { // If type is 0 (undefined), assume ARGB for compatibility or RGB for non-alpha
            BufferedImage.TYPE_INT_ARGB // Using ARGB by default for more robust transparency support
        } else {
            image.type
        }
    }

    /**
     * 处理一批图片路径，旋转它们并覆盖原文件。
     *
     * @param imagePaths 输入图片文件路径数组。
     * @param angle      旋转角度 (90, 180, 270)。
     */
    fun processAndOverwriteImageBatch(imagePaths: Array<String>, angle: Int) {
        if (imagePaths.isEmpty()) {
            println("没有图片路径需要处理。")
            return
        }
        println("--- 旋转 $angle 度图片并覆盖 ---")
        for (path in imagePaths) {
            if (path.isEmpty()) {
                continue
            }
            val inputFile = File(path)
            if (!inputFile.exists() || !inputFile.isFile) {
                System.err.println("输入文件不存在或不是文件: $path")
                continue
            }

            // 获取原始文件格式，以便以相同格式写入
            val formatName = getImageFormatName(path)
            if (formatName.isEmpty()) {
                System.err.println("无法确定文件格式或不支持此格式进行写入: $path")
                continue
            }

            try {
                // 读取原始图片
                val originalImage = FileInputStream(inputFile).use { ImageIO.read(it) }

                if (originalImage == null) {
                    System.err.println("无法读取图像 (格式不受支持或文件损坏): $path")
                    continue
                }

                // 旋转图片
                val rotatedImage = rotateImage(originalImage, angle)
                if (rotatedImage == null) {
                    System.err.println("图片旋转失败: $path")
                    continue
                }

                // 将旋转后的图片写入到原始文件的路径，覆盖原文件
                val success = FileOutputStream(inputFile).use { ImageIO.write(rotatedImage, formatName, it) }

                if (success) {
                    println("成功将 '$path' 旋转 $angle 度并覆盖原文件。")
                } else {
                    System.err.println("写入旋转后的图像失败（覆盖原文件）: $path。格式: $formatName。ImageIO 可能没有对应的写入器。")
                }

            } catch (e: IOException) {
                System.err.println("处理 '$path' 时发生 IO 错误: ${e.message}")
                e.printStackTrace()
            } catch (e: Exception) {
                System.err.println("处理 '$path' 时发生未知错误: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    /**
     * 根据文件路径获取适合 ImageIO.write 的图像格式名称。
     */
    private fun getImageFormatName(filePath: String): String {
        val dotIndex = filePath.lastIndexOf('.')
        if (dotIndex > 0 && dotIndex < filePath.length - 1) {
            val ext = filePath.substring(dotIndex + 1).lowercase()
            return when (ext) {
                "jpg", "jpeg" -> "jpeg"
                "png" -> "png"
                "gif" -> "gif"
                "bmp" -> "bmp"
                "tif", "tiff" -> "tiff" // 添加对TIFF的支持
                "webp" -> "webp" // ImageIO可能支持读取，但写入支持不佳，可尝试
                else -> {
                    System.err.println("警告: 未知图片扩展名 '$ext'。")
                    "" // 返回空字符串表示未知或不支持写入
                }
            }
        }
        return "" // 如果没有扩展名，返回空字符串
    }

    /**
     * 辅助方法：创建用于测试的虚拟图片。
     * 绘制一个简单的图案以区分方向。
     */
    fun createDummyImage(path: String, width: Int, height: Int) {
        val f = File(path)
        // 确保父目录存在
        f.parentFile?.mkdirs()
        if (f.exists()) {
            println("虚拟图片 $path 已存在。")
            return
        }
        try {
            val dummyImage = BufferedImage(width, height, BufferedImage.TYPE_INT_RGB)
            val g: Graphics2D = dummyImage.createGraphics()
            g.color = java.awt.Color.RED
            g.fillRect(0, 0, width / 2, height / 2) // 左上角红色
            g.color = java.awt.Color.GREEN
            g.fillRect(width / 2, 0, width / 2, height / 2) // 右上角绿色
            g.color = java.awt.Color.BLUE
            g.fillRect(0, height / 2, width / 2, height / 2) // 左下角蓝色
            g.color = java.awt.Color.YELLOW
            g.fillRect(width / 2, height / 2, width / 2, height / 2) // 右下角黄色
            g.dispose()
            val format = path.substring(path.lastIndexOf('.') + 1)
            FileOutputStream(f).use { ImageIO.write(dummyImage, format, it) }
            println("已创建虚拟图片: $path")
        } catch (e: IOException) {
            System.err.println("创建虚拟图片失败: $path - ${e.message}")
            e.printStackTrace()
        }
    }
}

// Windows 平台运行的入口点
fun main() {
    // 创建用于桌面测试的目录
    val desktopTestDir = "test_images_kotlin_overwrite"

    // --- 创建一些用于测试的虚拟图片 ---
    // 为了观察覆盖效果，建议每次运行时删除这些图片，或者将它们复制到新的测试目录
    /*val img90A = "$desktopTestDir${File.separator}sample_90_A.png"
    val img90B = "$desktopTestDir${File.separator}sample_90_B.jpg"
    val img180C = "$desktopTestDir${File.separator}sample_180_C.png"
    val img270D = "$desktopTestDir${File.separator}sample_270_D.jpg"

    ImageRotatorKotlin.createDummyImage(img90A, 100, 150)
    ImageRotatorKotlin.createDummyImage(img90B, 200, 100)
    ImageRotatorKotlin.createDummyImage(img180C, 120, 120)
    ImageRotatorKotlin.createDummyImage(img270D, 150, 180)

    val imagesToRotate90 = arrayOf(
        img90A,
        img90B
    )
    val imagesToRotate180 = arrayOf(
        img180C
    )
    val imagesToRotate270 = arrayOf(
        img270D
    )*/

    // --- 定义需要旋转并覆盖原文件的图片路径数组 ---
    // 请务必备份你的原始图片，因为这些操作会直接修改文件！
    // 为了提高效率，只要有一个数组的元素达到5个，就触发程序的执行，然后回撤所有数组元素为空字符串
    val imagesToRotate90 = arrayOf(
        "",
        "",
        "",
        "",
        ""
    )
    val imagesToRotate180 = arrayOf(
        "",
        "",
        "",
        "",
        ""
    )
    val imagesToRotate270 = arrayOf(
        "",
        "",
        "",
        "",
        ""
    )

    println("--- 开始桌面/Windows 平台图片处理 (Kotlin 版本 - 覆盖原文件) ---")
    println("注意：此操作会直接修改您的原始图片文件，请务必备份！")

    ImageRotatorKotlin.processAndOverwriteImageBatch(imagesToRotate90, 90)

    ImageRotatorKotlin.processAndOverwriteImageBatch(imagesToRotate180, 180)

    ImageRotatorKotlin.processAndOverwriteImageBatch(imagesToRotate270, 270)

    println("桌面处理完成。请直接检查原始图片文件，它们应该已经被旋转。")
}