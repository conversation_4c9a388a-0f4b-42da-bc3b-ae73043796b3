package com.test.android.common;


import com.google.common.base.Strings;

import java.io.UnsupportedEncodingException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class CommonStringUtils {
    /**
     * @param str
     * @return
     * @todo 判断是否包含中文字符
     */
    public static boolean isContainChinese(String str) {
        String regEx = "[\\u4e00-\\u9fbf]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param charaString
     * @return
     * @todo judge whether all is english.
     */
    public static boolean isAllEnglish(String charaString) {
        return charaString.matches("^[a-zA-Z]*");
    }

    public static boolean checkStringContainChinese(String checkStr) {
        if (!Strings.isNullOrEmpty(checkStr)) {
            char[] checkChars = checkStr.toCharArray();
            for (int i = 0; i < checkChars.length; i++) {
                char checkChar = checkChars[i];
                if (checkCharContainChinese(checkChar)) {
                    return true;
                }
            }
        }
        return false;
    }

    private static boolean checkCharContainChinese(char checkChar) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(checkChar);
        if (Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS == ub || Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS == ub ||
                Character.UnicodeBlock.CJK_COMPATIBILITY_FORMS == ub ||
                Character.UnicodeBlock.CJK_RADICALS_SUPPLEMENT == ub || Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A ==
                ub || Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B ==
                ub) {
            return true;
        }
        return false;
    }


    /**
     * Unicode convert to Chinese String.
     * @param str
     * @return
     */
    public static String unicodeToString(String str) {
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(str);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            str = str.replace(matcher.group(1), ch + "");
        }
        return str;
    }

    /**
     * String convert to Unicode.
     * <p>
     * do not set param string as Chinese Words directly.
     * <p>
     * We should get the unicode of Chinese Words by "http://tool.chinaz.com/tools/unicode.aspx" first,then compare two Unicode.
     *
     * @param string
     * @return
     */
    public static String getUnicode(String string) {
        try {
            StringBuffer out = new StringBuffer("");
            byte[] bytes = string.getBytes("unicode");
            for (int i = 0; i < bytes.length - 1; i += 2) {
                out.append("\\u");
                String str = Integer.toHexString(bytes[i + 1] & 0xff);
                for (int j = str.length(); j < 2; j++) {
                    out.append("0");
                }
                String str1 = Integer.toHexString(bytes[i] & 0xff);
                out.append(str1);
                out.append(str);

            }
            return out.toString();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }
}
