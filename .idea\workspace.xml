<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_main.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="314914bf-dc0e-4c77-85cd-70b07c3d1546" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[]" />
  <component name="ExportToHTMLSettings">
    <option name="OUTPUT_DIRECTORY" value="$PROJECT_DIR$/../TestAndroidJavaDemo\exportToHTML" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="TestAndroid" type="f1a62948:ProjectNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Kotlin File" />
        <option value="Class" />
        <option value="Kotlin Class" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2cx1nr2vxonWjfF073O4UU6Y9TX" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Gradle.TestAndroidJavaDemo [dependencies].executor": "Run",
    "Gradle.TestAndroidJavaDemo:smartjava [:smartjava:com.deepseek.BotChatCompletionsExample.main()].executor": "Run",
    "Gradle.TestAndroidJavaDemo:smartjava [:smartjava:com.deepseek.BotChatCompletionsExample2.main()].executor": "Run",
    "Gradle.TestAndroidJavaDemo:smartjava [:smartjava:com.deepseek.ChatCompletionsExample.main()].executor": "Run",
    "Gradle.TestAndroidJavaDemo:smartjava [:smartjava:com.test.android.file.BatchConvertFileEncode.main()].executor": "Run",
    "Kotlin.BingoSpringBootForDeepSeekKt.executor": "Run",
    "Kotlin.BotChatCompletionsExample.executor": "Run",
    "Kotlin.BotChatCompletionsExample2.executor": "Run",
    "Kotlin.ChatCompletionsExample.executor": "Run",
    "Kotlin.ConvertPdfToImagesKt.executor": "Run",
    "Kotlin.CopyDirectoryStructureKt.executor": "Run",
    "Kotlin.EdgeFold.executor": "Run",
    "Kotlin.MoveDirStrucAndPhoneFilesKt.executor": "Run",
    "Kotlin.MoveDirectoryStructureFilesKt.executor": "Run",
    "Kotlin.RenamePhoneFileAIKt.executor": "Run",
    "Kotlin.RenamePhoneFileKt.executor": "Run",
    "Kotlin.RepaymentInterestRate2Kt.executor": "Run",
    "Kotlin.RepaymentInterestRateKt.executor": "Run",
    "Kotlin.RotateImagesKt.executor": "Run",
    "Kotlin.SyncPhoneFilesKt.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "com.google.services.firebase.aqiPopupShown": "true",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "F:/Temp/Download/intellij-augment-0.217.0.zip",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.17",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "show.do.not.copy.http.proxy.settings.to.gradle": "false"
  }
}]]></component>
  <component name="PsdUISettings">
    <option name="MODULE_TAB" value="Signing Configs" />
    <option name="LAST_EDITED_SIGNING_CONFIG" value="debug" />
    <option name="LAST_EDITED_BUILD_TYPE" value="release" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="H:\develop\Studio_WorkPlace\TestAndroidJavaDemo\smartjava\src\main\java\com\test\android\file\sync" />
      <recent name="H:\Project\TestAndroidJavaDemo\smartjava\src\main\java\com\bingo\core" />
      <recent name="H:\AutoSmart\Project\TestAndroidJavaDemo\smartjava\src\main\java\com\test\android\jxbycgkt" />
      <recent name="H:\AutoSmart\Project\TestAndroidJavaDemo\smartjava\src\main\java\com\test\android\common" />
      <recent name="H:\AutoSmart\Project\TestAndroidJavaDemo\smartjava\src\main\java\com\test\android\file" />
    </key>
    <key name="MoveKotlinTopLevelDeclarationsDialog.RECENTS_KEY">
      <recent name="com.bingo.core" />
    </key>
    <key name="K2MoveDeclarationsDialog.RECENT_PACKAGE_KEY">
      <recent name="com.test.android.file.sync" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.deepseek" />
    </key>
  </component>
  <component name="RunManager" selected="Kotlin.SyncPhoneFilesKt">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="TestAndroid.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RenamePhoneFileAIKt" type="JetRunConfigurationType" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.test.android.file.sync.RenamePhoneFileAIKt" />
      <module name="TestAndroid.smartjava.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RepaymentInterestRate2Kt" type="JetRunConfigurationType" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="RepaymentInterestRate2Kt" />
      <module name="TestAndroid.smartjava.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RepaymentInterestRateKt" type="JetRunConfigurationType" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.test.android.RepaymentInterestRateKt" />
      <module name="TestAndroid.smartjava.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RotateImagesKt" type="JetRunConfigurationType" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.test.android.image.RotateImagesKt" />
      <module name="TestAndroid.smartjava.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SyncPhoneFilesKt" type="JetRunConfigurationType" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.test.android.file.sync.SyncPhoneFilesKt" />
      <module name="TestAndroid.smartjava.main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Android App.app" />
      <item itemvalue="Kotlin.RepaymentInterestRate2Kt" />
      <item itemvalue="Kotlin.RotateImagesKt" />
      <item itemvalue="Kotlin.RepaymentInterestRateKt" />
      <item itemvalue="Kotlin.SyncPhoneFilesKt" />
      <item itemvalue="Kotlin.RenamePhoneFileAIKt" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Kotlin.SyncPhoneFilesKt" />
        <item itemvalue="Kotlin.RepaymentInterestRate2Kt" />
        <item itemvalue="Kotlin.RepaymentInterestRateKt" />
        <item itemvalue="Kotlin.RenamePhoneFileAIKt" />
        <item itemvalue="Kotlin.RotateImagesKt" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="314914bf-dc0e-4c77-85cd-70b07c3d1546" name="Changes" comment="" />
      <created>1709038588334</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1709038588334</updated>
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="kotlin-line">
          <condition expression="edgeFoldWidth==170" language="kotlin" />
          <url>file://$PROJECT_DIR$/smartjava/src/main/java/com/test/android/jxbycg/EdgeFold.kt</url>
          <line>178</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <condition expression="edgeFoldWidth==170" language="kotlin" />
          <url>file://$PROJECT_DIR$/smartjava/src/main/java/com/test/android/jxbycg/EdgeFold.kt</url>
          <line>223</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/smartjava/src/main/java/com/test/android/file/sync/MoveDirStrucAndPhoneFiles.kt</url>
          <line>127</line>
          <option name="timeStamp" value="30" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/smartjava/src/main/java/com/bingo/core/LoggerRateLimiter.kt</url>
          <line>18</line>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/smartjava/src/main/java/com/test/android/jxbycg/EdgeFoldModel.java</url>
          <line>16</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/smartjava/src/main/java/com/test/android/jxbycg/EdgeFoldModel.java</url>
          <line>28</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.volcengine/volcengine-java-sdk-ark-runtime/0.1.151/2caf4503c69c42f522d11f2e9660e612463d0cea/volcengine-java-sdk-ark-runtime-0.1.151-sources.jar!/com/volcengine/ark/runtime/service/ArkService.java</url>
          <line>414</line>
          <option name="timeStamp" value="29" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.test.android.jxbycg.EdgeFoldItem" memberName="blankingColorPlateModel" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.test.android">
          <value>
            <CheckInfo lastCheckTimestamp="1746120169557" />
          </value>
        </entry>
        <entry key="com.test.android.test">
          <value>
            <CheckInfo lastCheckTimestamp="1746120169557" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>