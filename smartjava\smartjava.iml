<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":smartjava" external.linked.project.path="$MODULE_DIR$" external.root.project.path="$MODULE_DIR$/.." external.system.id="GRADLE" external.system.module.group="TestAndroid" external.system.module.version="unspecified" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android-gradle" name="Android-Gradle">
      <configuration>
        <option name="GRADLE_PROJECT_PATH" value=":smartjava" />
        <option name="LAST_SUCCESSFUL_SYNC_AGP_VERSION" />
        <option name="LAST_KNOWN_AGP_VERSION" />
      </configuration>
    </facet>
    <facet type="java-gradle" name="Java-Gradle">
      <configuration>
        <option name="BUILD_FOLDER_PATH" value="$MODULE_DIR$/build" />
        <option name="BUILDABLE" value="true" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_7">
    <output url="file://$MODULE_DIR$/build/classes/java/main" />
    <output-test url="file://$MODULE_DIR$/build/classes/java/test" />
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/.gradle" />
      <excludeFolder url="file://$MODULE_DIR$/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" exported="" scope="PROVIDED" name="Gradle: commons-codec-1.10" level="project" />
    <orderEntry type="library" exported="" scope="PROVIDED" name="Gradle: commons-io-1.4" level="project" />
    <orderEntry type="library" exported="" scope="PROVIDED" name="Gradle: commons-io" level="project" />
    <orderEntry type="library" exported="" scope="PROVIDED" name="Gradle: guava-19.0" level="project" />
    <orderEntry type="library" exported="" scope="PROVIDED" name="Gradle: JsonUtils" level="project" />
    <orderEntry type="library" exported="" scope="PROVIDED" name="Gradle: registry" level="project" />
    <orderEntry type="library" exported="" scope="RUNTIME" name="Gradle: commons-codec-1.10" level="project" />
    <orderEntry type="library" exported="" scope="RUNTIME" name="Gradle: commons-io-1.4" level="project" />
    <orderEntry type="library" exported="" scope="RUNTIME" name="Gradle: commons-io" level="project" />
    <orderEntry type="library" exported="" scope="RUNTIME" name="Gradle: guava-19.0" level="project" />
    <orderEntry type="library" exported="" scope="RUNTIME" name="Gradle: JsonUtils" level="project" />
    <orderEntry type="library" exported="" scope="RUNTIME" name="Gradle: registry" level="project" />
    <orderEntry type="library" exported="" scope="TEST" name="Gradle: commons-codec-1.10" level="project" />
    <orderEntry type="library" exported="" scope="TEST" name="Gradle: commons-io-1.4" level="project" />
    <orderEntry type="library" exported="" scope="TEST" name="Gradle: commons-io" level="project" />
    <orderEntry type="library" exported="" scope="TEST" name="Gradle: guava-19.0" level="project" />
    <orderEntry type="library" exported="" scope="TEST" name="Gradle: JsonUtils" level="project" />
    <orderEntry type="library" exported="" scope="TEST" name="Gradle: registry" level="project" />
  </component>
</module>