package com.test.memoryleak;

import android.app.Activity;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.widget.TextView;

import com.test.android.R;

import java.util.List;
import java.util.Properties;

public class MemoryLeakActivity6 extends Activity {
    TextView textView;
    private int timer;
    private Handler handler = new Handler();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.memory_leak_layout);
        textView = (TextView) findViewById(R.id.text_view);
    }

    @Override
    protected void onResume() {
        super.onResume();
        startAsyncTask();

        handler.post(new Runnable() {
            @Override
            public void run() {
                textView.setText(String.valueOf(timer++));
                handler.postDelayed(this, 1000);
            }
        });
    }

    void startAsyncTask() {
        // This async task is an anonymous class and therefore has a hidden reference to the outer
        // class MainActivity. If the activity gets destroyed before the task finishes (e.g. rotation),
        // the activity instance will leak.
        new AsyncTask<Void, Void, Void>() {
            @Override protected Void doInBackground(Void... params) {
                // Do some slow work in background
                SystemClock.sleep(20000);
                return null;
            }
        }.execute();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        /*if (handler != null) {
            handler.removeCallbacksAndMessages(null);
            handler = null;
        }*/
        /*MyApplication.getRefWatcher(this).watch(this);*/


    }
}
