package com.common.utils;


import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

public class FileUtils {
    //充分利用commons-io-1.4.jar包的工具类
    /*import org.apache.commons.io.FileUtils;
    //删除文件夹
    FileUtils.deleteDirectory(new File(genPath));
    //列出所有的文件
    Collection<File> files = (Collection<File>) FileUtils.listFiles(new File(dir), null, true);*/


    /**
     * Copy data from a source stream to destFile.
     * Return true if succeed, return false if failed.
     */
    private static boolean copyToFile(InputStream inputStream, File destFile) {
        boolean result = false;
        OutputStream out = null;
        try {
            if (!destFile.getParentFile().exists()) {
                destFile.getParentFile().mkdirs();
            } else if (destFile.exists()) {
                destFile.delete();
            }
            out = new FileOutputStream(destFile);

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) >= 0) {
                out.write(buffer, 0, bytesRead);
            }

            result = true;
        } catch (Exception e) {
            result = false;
        } finally {
            if (out != null) {
                try {
                    out.flush();
                } catch (Exception e) {
                    result = false;
                }
                try {
                    out.close();
                } catch (Exception e) {
                    result = false;
                }
            }
        }
        if (!result) {
            destFile.delete();
        }
        return result;
    }

    /**
     * copy file to another place.
     *
     * @param srcFile
     * @param destFile
     * @return
     */
    public static boolean copyFile(File srcFile, File destFile) {
        boolean result = false;
        if (srcFile != null && srcFile.exists()) {
            InputStream in = null;
            try {
                in = new FileInputStream(srcFile);
                result = copyToFile(in, destFile);
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                if (in != null) {
                    try {
                        in.close();
                    } catch (Exception ex) {
                    }
                }
            }
        }
        return result;
    }

    /**
     * * 从绝对路径，获取文件后缀
     *
     * @param fileName
     * @return
     */
    public static String getSuffix(String fileName) {
        String suffix = "";
        if (fileName != null) {
            int lastDot = fileName.lastIndexOf(".");
            if (lastDot != -1 && lastDot != 0 && lastDot != fileName.length() - 1) {
                suffix = fileName.substring(lastDot + 1, fileName.length()).toLowerCase();
            }
        }
        return suffix;
    }

    /**
     * * 从绝对路径，获取文件名称
     *
     * @param filePath
     * @return
     */
    public static String getFileNameFromPath(String filePath) {
        String suffix = "";
        if (filePath != null) {
            int lastDot = filePath.lastIndexOf("/");
            if (lastDot != -1 && lastDot != 0 && lastDot != filePath.length() - 1) {
                suffix = filePath.substring(lastDot + 1, filePath.length()).toLowerCase();
            }
        }
        return suffix;
    }

    /**
     * 确保成功的创建一个可以访问的目录；如果原来有同名的文件，则先删除，再创建目录。
     * @param folderPath
     */
    public static void createEnableDir(String folderPath) {
        File dir = new File(folderPath);
        if (dir.exists()) {
            if (!dir.isDirectory()) {
                dir.delete();
                dir.mkdirs();
            }
        } else {
            dir.mkdirs();
        }
    }


}
