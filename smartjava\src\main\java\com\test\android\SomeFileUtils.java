package com.test.android;


import com.test.android.common.Constants;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Provider;
import java.security.Security;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

public class SomeFileUtils {
    private static char[] hexChar = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /**
     * @param file
     * @return
     * @throws FileNotFoundException
     * @todo 获取小于1G的文件的md5的方式一
     */
    public static String getMd5OfSmallFile1(File file) throws FileNotFoundException {
        String value = null;
        FileInputStream in = new FileInputStream(file);
        try {
            MappedByteBuffer byteBuffer = in.getChannel().map(FileChannel.MapMode.READ_ONLY, 0, file.length());
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(byteBuffer);
            BigInteger bi = new BigInteger(1, md5.digest());
            value = bi.toString(16);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return value;
    }

    /**
     * @param path
     * @return
     * @todo 获取小于1G的文件的md5的方式二
     */
    public static String getMd5OfSmallFile2(String path) {
        try {
            FileInputStream fis = new FileInputStream(path);
            String md5 = DigestUtils.md5Hex(IOUtils.toByteArray(fis));
            IOUtils.closeQuietly(fis);
            System.out.println("path:" + path + "  MD5:" + md5);
            return md5;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getMd5OfLargeFile(String filePath) throws IOException, NoSuchAlgorithmException {
        long start = System.currentTimeMillis();
        System.out.println("begin to calculate md5 of big file,hold on...");
        // String fileName = "E:\\Office_2010_Toolkit_2.2.3_XiaZaiBa.zip";
        String hash = getHash(filePath, "MD5");
        System.out.println("MD5:" + hash);
        long end = System.currentTimeMillis();
        System.out.println("total cost time:" + (end - start) + "ms");
        return hash;
    }

    private static String getHash(String filePath, String hashType) throws IOException, NoSuchAlgorithmException {
        File file = new File(filePath);
        System.out.println(" -------------------------------------------------------------------------------");
        System.out.println("|file name:" + file.getName());
        System.out.println("|file size:" + (file.length() / Constants.SIZE_MB) + "SIZE_MB");
        System.out.println("|file path[absolute]:" + file.getAbsolutePath());
        System.out.println("|file path[---]:" + file.getCanonicalPath());
        System.out.println(" -------------------------------------------------------------------------------");

        InputStream ins = new FileInputStream(file);
        byte[] buffer = new byte[8192];
        MessageDigest md5 = MessageDigest.getInstance(hashType);

        int len;
        while ((len = ins.read(buffer)) != -1) {
            md5.update(buffer, 0, len);
        }

        ins.close();
        // 也可以用apache自带的计算MD5方法
        return DigestUtils.md5Hex(md5.digest());
        // 自己写的转计算MD5方法
        // return toHexString(md5.digest());
    }

    protected static String toHexString(byte[] b) {
        StringBuilder sb = new StringBuilder(b.length * 2);
        for (int i = 0; i < b.length; i++) {
            sb.append(hexChar[(b[i] & 0xf0) >>> 4]);
            sb.append(hexChar[b[i] & 0x0f]);
        }
        return sb.toString();
    }

    /**
     * @param serviceType
     * @return
     * @todo 获取MessageDigest支持几种加密算法
     */
    private static String[] getCryptolmpls(String serviceType) {
        Set result = new HashSet();
        // all providers
        Provider[] providers = Security.getProviders();
        for (int i = 0; i < providers.length; i++) {
            // get services provided by each provider
            Set keys = providers[i].keySet();
            for (Iterator it = keys.iterator(); it.hasNext(); ) {
                String key = it.next().toString();
                key = key.split(" ")[0];

                if (key.startsWith(serviceType + ".")) {
                    result.add(key.substring(serviceType.length() + 1));
                } else if (key.startsWith("Alg.Alias." + serviceType + ".")) {
                    result.add(key.substring(serviceType.length() + 11));
                }
            }
        }
        return (String[]) result.toArray(new String[result.size()]);
    }

    /**
     * 复制文件夹内的全部内容；递归调用。
     *
     * @param src
     * @param des
     * @throws Exception
     */
    public static void dirCopy(String src, String des) throws Exception {
        //初始化文件复制
        File srcFile = new File(src);
        //把文件里面内容放进数组
        File[] srcFiles = srcFile.listFiles();
        //初始化文件粘贴
        File desFile = new File(des);
        //判断是否有这个文件。有不管,没有创建
        if (!desFile.exists()) {
            desFile.mkdirs();
        }
        //遍历文件及文件夹
        for (File f : srcFiles) {
            if (f.isFile()) {
                //文件
                copySingleFile(f.getPath(), des + "\\" + f.getName()); //调用文件拷贝的方法
            } else if (f.isDirectory()) {
                //文件夹
                dirCopy(f.getPath(), des + "\\" + f.getName());//继续调用复制方法      递归的地方,自己调用自己的方法,就可以复制文件夹的文件夹了
            }
        }
    }


    /**
     * 递归删除目录的全部内容
     *
     * @param src
     * @throws Exception
     */
    public static void dirDelete(String src) throws Exception {
        File srcFile = new File(src);
        File[] srcFiles = srcFile.listFiles();
        //遍历文件及文件夹；如果为空的文件夹，则直接跳过这段遍历代码。
        for (File f : srcFiles) {
            if (f.isFile()) {
                //文件直接删除
                f.delete();
            } else if (f.isDirectory()) {
                //文件夹递归遍历删除
                dirDelete(f.getPath());
            }
        }
        //删除空的文件夹；清空非空文件夹中的文件后，把文件夹删除。
        srcFile.delete();
    }


    /**
     * 复制单个文件
     *
     * @param oldFilePath String  原文件路径  如：c:/fqf.txt
     * @param newFilePath String  复制后路径  如：f:/fqf.txt
     * @return boolean
     * @time 2016-12-04
     */
    public static boolean copySingleFile(String oldFilePath, String newFilePath) {
        try {
            // int bytesum = 0;
            int byteread = 0;
            File oldfile = new File(oldFilePath);
            if (!oldfile.exists()) {
                return false;
            }
            if (new File(newFilePath).exists()) {
                return false;
            }
            InputStream inStream = new FileInputStream(oldFilePath); // 读入原文件
            FileOutputStream fs = new FileOutputStream(newFilePath);
            byte[] buffer = new byte[1024];
            // int length;
            while ((byteread = inStream.read(buffer)) != -1) {
                // bytesum += byteread; //字节数 文件大小
                // System.out.println(bytesum);
                fs.write(buffer, 0, byteread);
            }
            inStream.close();
            fs.close();
        } catch (Exception e) {
            System.out.println("error when dirCopy single file");
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * @param pathandname
     * @return
     * @todo 仅保留文件名不保留后缀
     * <AUTHOR>
     * @date 2017年1月7日 下午6:22:23
     */
    public static String getFileName(String pathandname) {
        int start = pathandname.lastIndexOf("\\");
        int end = pathandname.lastIndexOf(".");
        if (start > end) {
            return null;
        }
        if (start != -1 && end != -1) {
            return pathandname.substring(start + 1, end);
        } else {
            return null;
        }
    }

    /**
     * @param pathandname
     * @return
     * @todo 保留文件名及后缀
     * <AUTHOR>
     * @date 2017年1月7日 下午6:22:42
     */
    public static String getFileNameWithSuffix(String pathandname) {
        int start = pathandname.lastIndexOf("\\");
        if (start != -1) {
            return pathandname.substring(start + 1);
        } else {
            return null;
        }
    }

}
