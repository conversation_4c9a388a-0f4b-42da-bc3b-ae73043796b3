package com.test;

import android.util.Log;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class Downloader {
    public String data;

    public String getGen(String paramString) throws Exception {
        Log.d("", paramString);
        HttpURLConnection localHttpURLConnection = (HttpURLConnection) new URL("http://114.251.97.171:7600/SuperMoneyServer/servlet/BsServerOnline?" + paramString).openConnection();
        localHttpURLConnection.setConnectTimeout(5000);
        localHttpURLConnection.setRequestMethod("GET");
        if (localHttpURLConnection.getResponseCode() != 200)
            throw new RuntimeException("请求url失败");
        InputStream localInputStream = localHttpURLConnection.getInputStream();
        String str = readData(localInputStream);
        localInputStream.close();
        localHttpURLConnection.disconnect();
        this.data = str;
        return str;
    }

    /**
     * @ 作者：yh_android
     * @ 功能：账号314812/密码351914
     * @ 日期：2014-2-21 上午11:48:50
     */
    public String getStr(String name, String psd) throws Exception {
        if ((name == null) || (name.length() == 0) || (psd == null) || (psd.length() == 0))
            throw new IllegalArgumentException("账号、密码不能为空。");
        HttpURLConnection localHttpURLConnection = (HttpURLConnection) new URL("http://117.135.139.202/androidTool/gen.php?u=" + name + "&p=" + psd).openConnection();
        localHttpURLConnection.setConnectTimeout(5000);
        localHttpURLConnection.setRequestMethod("GET");
        if (localHttpURLConnection.getResponseCode() != 200)
            throw new RuntimeException("请求url失败");
        InputStream localInputStream = localHttpURLConnection.getInputStream();
        String str = readData(localInputStream);
        localInputStream.close();
        localHttpURLConnection.disconnect();
        this.data = str;
        return str;
    }

    public String readData(InputStream paramInputStream) throws Exception {
        ByteArrayOutputStream localByteArrayOutputStream = new ByteArrayOutputStream();
        byte[] arrayOfByte1 = new byte[1024];
        while (true) {
            int i = paramInputStream.read(arrayOfByte1);
            if (i == -1) {
                byte[] arrayOfByte2 = localByteArrayOutputStream.toByteArray();
                localByteArrayOutputStream.close();
                return new String(arrayOfByte2);
            }
            localByteArrayOutputStream.write(arrayOfByte1, 0, i);
        }
    }

    public void save() throws IOException {
        if (this.data == null)
            throw new IOException("要保存的数据不正确：" + this.data);
        save("mobileSecurity");
    }

    public void save(String paramString) throws IOException {
        Log.e(getClass().getSimpleName(), paramString);
        FileOutputStream localFileOutputStream = new FileOutputStream(new File("/mnt/sdcard/" + paramString));
        localFileOutputStream.write(this.data.getBytes());
        localFileOutputStream.close();
    }
}