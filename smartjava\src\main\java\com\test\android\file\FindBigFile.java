package com.test.android.file;

import com.test.android.common.Constants;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.Collection;

/**
 * @todo find big file list
 */
public class FindBigFile {
    private static final long BIG_FILE_SIZE = 500 * Constants.SIZE_MB;

    public static void main(String[] args) {
        String dir = "H:\\AutoSmart";
        Collection<File> files = (Collection<File>) FileUtils.listFiles(new File(dir), null, true);
        System.out.println("listfile count is:" + files.size());
        for (File file : files) {
            //String absolutePath = file.getAbsolutePath();
            if (file.length() >= BIG_FILE_SIZE) {
                System.out.println(file.getAbsolutePath());
            }
        }
    }

}
