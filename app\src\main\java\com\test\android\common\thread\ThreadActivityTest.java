package com.test.android.common.thread;

import android.app.Activity;
import android.os.Bundle;
import android.os.PersistableBundle;

import com.common.utils.Logs;

import java.util.Queue;
import java.util.Random;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @todo how to use ScheduledExecutorService and ThreadPoolExecutor.
 * interval some time to do something by HeartbeatTimerScheduled.java and ThreadPoolExecutorDemo.java multi-thread.
 * @time 2017/10/18 18:22
 */
public class ThreadActivityTest extends Activity {
    private final String TAG = this.getClass().getSimpleName();
    private HeartbeatTimerScheduled heartbeatTimer;
    private ThreadPoolExecutor mThreadPoolExecutor;

    @Override
    public void onCreate(Bundle savedInstanceState, PersistableBundle persistentState) {
        super.onCreate(savedInstanceState, persistentState);
    }

    private void startHeartbeatTimer() {
        Logs.i();
        heartbeatTimer = new HeartbeatTimerScheduled();
        heartbeatTimer.setOnScheduleListener(new HeartbeatTimerScheduled.OnScheduleListener() {
            @Override
            public void onSchedule() {
                //do something
                Logs.i();
                doSomeThing(new Random().nextInt());
            }
        });
        heartbeatTimer.startTimer(0, 1000 * 2);
    }

    @Override
    protected void onResume() {
        Logs.i();
        super.onResume();
        initThread();
        startHeartbeatTimer();
    }

    @Override
    protected void onStop() {
        Logs.i();
        if (mThreadPoolExecutor != null && !mThreadPoolExecutor.isShutdown()) {
            mThreadPoolExecutor.shutdown();
        }
        if (heartbeatTimer != null) {
            heartbeatTimer.exit();
        }
        super.onStop();
    }

    /**
     * 单个CPU线程池大小
     */
    private static final int POOL_SIZE = 5;
    /**
     * 队列深度
     */
    int queueDeep = 5;

    private void initThread() {
        /*
         * 创建线程池，最小线程数为2，最大线程数为4，线程池维护线程的空闲时间为3秒，
         * 使用队列深度为4的有界队列，如果执行程序尚未关闭，则位于工作队列头部的任务将被删除，
         * 然后重试执行程序（如果再次失败，则重复此过程），里面已经根据队列深度对任务加载进行了控制。
         */
        mThreadPoolExecutor = new ThreadPoolExecutor(2, 4, 3, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(queueDeep),
                new ThreadPoolExecutor.DiscardOldestPolicy());
    }

    class TaskThreadPoolRunnable implements Runnable {
        private int index;

        public TaskThreadPoolRunnable(int index) {
            this.index = index;
        }

        @Override
        public void run() {
            Logs.i(Thread.currentThread() + " index:" + index);
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private synchronized int getQueueSize(Queue queue) {
        return queue.size();
    }

    private void doSomeThing(int index) {
        while (getQueueSize(mThreadPoolExecutor.getQueue()) >= queueDeep) {
            //循环检测，如果size大于5，则等待3秒。
            Logs.i("准备添加" + index + "，但队列已满，等3秒再添加任务");
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        Logs.i("hello,ladies and gentleman！" + index);
        TaskThreadPoolRunnable taskThreadPoolRunnable = new TaskThreadPoolRunnable(index);
        Logs.i("already put i:" + index);
        mThreadPoolExecutor.execute(taskThreadPoolRunnable);
    }

    @Override
    protected void onDestroy() {
        Logs.i();
        super.onDestroy();
    }
}
