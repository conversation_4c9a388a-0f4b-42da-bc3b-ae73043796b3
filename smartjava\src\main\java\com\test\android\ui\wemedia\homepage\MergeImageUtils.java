package com.test.android.ui.wemedia.homepage;

import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

import javax.imageio.ImageIO;

public class MergeImageUtils {
    private Graphics2D graphics2D = null;

    /**
     * 导入本地图片到缓冲区
     */
    public BufferedImage loadImageLocal(String imgName) {
        try {
            return ImageIO.read(new File(imgName));
        } catch (IOException e) {
            System.out.println(e.getMessage());
        }
        return null;
    }

    /**
     * 两个图片都是.jpg格式的时候，可以正常的合并;
     * @param image1
     * @param image2
     * @return
     */
    public BufferedImage mergeImagetogeter(BufferedImage image1, BufferedImage image2) {
        try {
            int w = image1.getWidth();
            int h = image1.getHeight();
            graphics2D = image2.createGraphics();
            graphics2D.drawImage(image1, 0, 1167, w, h, null);
            graphics2D.dispose();
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return image2;
    }

    /**
     * 生成新图片到本地
     */
    public void writeImageLocal(String newImagePath, BufferedImage img) {
        if (newImagePath != null && img != null) {
            try {
                File outputfile = new File(newImagePath);
                ImageIO.write(img, "jpg", outputfile);
            } catch (IOException e) {
                System.out.println(e.getMessage());
            }
        }
    }
}
