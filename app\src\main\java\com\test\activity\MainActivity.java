package com.test.activity;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.Keyframe;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.SearchManager;
import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.res.Configuration;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.os.StatFs;
import android.speech.RecognizerIntent;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.common.utils.Logs;
import com.common.utils.PackageUtils;
import com.common.utils.PasswordEncryption;
import com.common.utils.SoundUtils;
import com.orhanobut.logger.LogLevel;
import com.orhanobut.logger.Logger;
import com.test.android.R;
import com.test.android.SizeConverter;
import com.test.android.common.thread.ThreadActivityTest;
import com.test.memoryleak.MemoryLeakActivity6;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.concurrent.CountDownLatch;

public class MainActivity extends Activity implements LocationListener {
    private final String TAG = this.getClass().getSimpleName();
    private Context mContext;
    private SoundUtils mSoundUtils;

    private TextView mTxt;
    private Button btnThread, btnClickPlaySound, btnCopyDatabase, btnStartVoice, btnMemoryLeak, btn_ClipBook, btn_animation,
            btn_property_anim, btn_property_anim2, btn_property_anim3;
    private ImageView iv_fill, iv_empty, iv_anim_selector;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Logs.i();
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        mContext = this;
        initView();
        initData();
        readSystem();
        readSDCard();

        initLogger();
        Logger.v(TAG, "TEST_MESSAGE");
        //isAppInstalled();

        initSoundPlay();

        long num = Long.MAX_VALUE >> 1;
        Logs.i("num:" + num);
        long num2 = Long.MAX_VALUE;
        Logs.i("num2:" + num2);
        //setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
    }


    private void initData() {
        //mTxt.setText(new JniTest().getString());//flagtest temp remove
    }

    private void initView() {
        mTxt = (TextView) findViewById(R.id.txt);
        btnThread = (Button) findViewById(R.id.btn_thread);
        btnThread.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent();
                intent.setClass(mContext, ThreadActivityTest.class);
                startActivity(intent);
            }
        });
        btnClickPlaySound = (Button) findViewById(R.id.btn_play_sound);
        btnClickPlaySound.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mSoundUtils.playSound();
            }
        });
        btnCopyDatabase = (Button) findViewById(R.id.btn_copy_database);
        btnCopyDatabase.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                copyOutDatabase();
            }
        });
        btnStartVoice = (Button) findViewById(R.id.btn_start_voice);
        btnStartVoice.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                startVoice();
            }
        });
        btnMemoryLeak = (Button) findViewById(R.id.btn_memory_leak);
        btnMemoryLeak.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent();
                intent.setClass(mContext, MemoryLeakActivity6.class);
                startActivity(intent);
            }
        });
        btn_ClipBook = (Button) findViewById(R.id.btn_ClipBook);
        btn_animation = (Button) findViewById(R.id.btn_animation);
        btn_property_anim = (Button) findViewById(R.id.btn_property_anim);
        btn_property_anim2 = (Button) findViewById(R.id.btn_property_anim2);
        btn_property_anim3 = (Button) findViewById(R.id.btn_property_anim3);
        btn_ClipBook.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //showClipBook(mContext, mClipBookHandler);
                insertText(mContext, "sdfadsfd");
            }
        });
        iv_empty = (ImageView) findViewById(R.id.iv_empty);
        iv_fill = (ImageView) findViewById(R.id.iv_fill);


        testForTweenAnimation();
        testForFrameAnimation();
        testForPropertyAnimation();
    }

    private void testForTweenAnimation() {
        btn_animation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //test for tween animation
                Animation loadAnimation = AnimationUtils.loadAnimation(mContext, R.anim.animation1);
                loadAnimation.setFillAfter(true);
                btn_animation.startAnimation(loadAnimation);
                loadAnimation.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                        Logs.d();
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        Logs.d();
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {
                        Logs.d();
                    }
                });
            }
        });
    }

    private void testForFrameAnimation() {
        ((AnimationDrawable) iv_empty.getBackground()).start();
        ((AnimationDrawable) iv_fill.getBackground()).start();
        iv_anim_selector = (ImageView) findViewById(R.id.iv_anim_selector);
    }

    private void testForPropertyAnimation() {
        //init object animator by XML code.
        /*btn_property_anim = (Button) findViewById(R.id.btn_property_anim);
        Animator animator = AnimatorInflater.loadAnimator(this, R.animator.animator_alpha);
        animator.setTarget(btn_property_anim);
        animator.start();*/


        btn_property_anim.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //init object animator by java code.
                /*ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(btn_property_anim, "alpha", 0f, 1f);
                alphaAnimator.setDuration(9000);
                alphaAnimator.setRepeatCount(5);
                alphaAnimator.setRepeatMode(ValueAnimator.REVERSE);
                alphaAnimator.setStartDelay(200);
                alphaAnimator.setInterpolator(new AccelerateDecelerateInterpolator());*/
                //alphaAnimator.start();

                /*ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(btn_property_anim, "scaleX", 1.0f, 1.5f);
                scaleXAnimator.setDuration(9000);
                scaleXAnimator.setRepeatCount(ValueAnimator.INFINITE);
                scaleXAnimator.setRepeatMode(ValueAnimator.REVERSE);
                scaleXAnimator.setStartDelay(200);
                scaleXAnimator.setInterpolator(new AccelerateDecelerateInterpolator());*/
                //scaleXAnimator.start();

                /*ObjectAnimator rotationAnimator = ObjectAnimator.ofFloat(btn_property_anim, "rotation", 0f, 360f);
                rotationAnimator.setDuration(3000);
                rotationAnimator.setRepeatCount(ValueAnimator.INFINITE);
                rotationAnimator.setRepeatMode(ValueAnimator.REVERSE);*/
                //rotationAnimator.start();

                ObjectAnimator translateAnimatorX = ObjectAnimator.ofFloat(btn_property_anim, "scaleX", 1f, 1.5f);
                ObjectAnimator translateAnimatorY = ObjectAnimator.ofFloat(btn_property_anim, "scaleY", 1f, 1.5f);
                translateAnimatorX.setDuration(9000);
                translateAnimatorX.setRepeatCount(ValueAnimator.INFINITE);
                translateAnimatorX.setRepeatMode(ValueAnimator.RESTART);
                translateAnimatorX.setStartDelay(200);

                translateAnimatorY.setDuration(9000);
                translateAnimatorY.setRepeatCount(ValueAnimator.INFINITE);
                translateAnimatorY.setRepeatMode(ValueAnimator.RESTART);
                translateAnimatorY.setStartDelay(200);
                //play single property animation.
                //translateAnimatorX.start();

                translateAnimatorX.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animator) {
                        Logs.d("translateAnimatorX---onAnimationStart");
                    }

                    @Override
                    public void onAnimationEnd(Animator animator) {
                        Logs.d("translateAnimatorX---onAnimationEnd");
                    }

                    @Override
                    public void onAnimationCancel(Animator animator) {
                        Logs.d("translateAnimatorX---onAnimationCancel");
                    }

                    @Override
                    public void onAnimationRepeat(Animator animator) {
                        Logs.d("translateAnimatorX---onAnimationRepeat");
                    }
                });


                /*使用AnimatorListenerAdapter代替AnimatorListener
                由于AnimatorListener是接口，所以实现它得实现它所有的方法，而我们有时只用到它的个别回调（如：onAnimationStart），
                使用它会导致代码看起来非常冗杂。
                而AnimatorListenerAdapter是默认实现了AnimatorListener的一个抽象类，你可以按需要重写其中的方法，代码会优雅一点。*/
                translateAnimatorX.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationPause(Animator animation) {
                        super.onAnimationPause(animation);
                    }
                });

                translateAnimatorX.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator valueAnimator) {
                        float value = (float) valueAnimator.getAnimatedValue();
                        //可以根据自己的需要来获取动画更新值。
                        Logs.d("AnimatorUpdateListener", "the animation value is " + value);
                        Logs.d("translateAnimatorX---onAnimationUpdate");
                    }
                });

                final AnimatorSet animatorSet = new AnimatorSet();
                //animatorSet.playTogether(alphaAnimator, scaleXAnimator, rotationAnimator, translateAnimatorX);
                animatorSet.playSequentially(/*alphaAnimator, scaleXAnimator, rotationAnimator,*/ translateAnimatorX);
                //many property animations play together.
        /*animatorSet.setDuration(9000);
        animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());*/

                //why does the animatorset run only once? flagtest
                animatorSet.start();
            }
        });


        btn_property_anim2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //use animate()
                btn_property_anim2.animate().setDuration(2000).scaleXBy(1.0f).scaleYBy(1.0f).setListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animator) {

                    }

                    @Override
                    public void onAnimationEnd(Animator animator) {

                    }

                    @Override
                    public void onAnimationCancel(Animator animator) {

                    }

                    @Override
                    public void onAnimationRepeat(Animator animator) {

                    }
                }).setStartDelay(200).setUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator valueAnimator) {

                    }
                }).start();
            }
        });

        btn_property_anim3.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Keyframe k0 = Keyframe.ofFloat(0f, 0); //第一个参数为“何时”，第二个参数为“何地”
                Keyframe k1 = Keyframe.ofFloat(0.5f, 50);
                Keyframe k2 = Keyframe.ofFloat(1f, 0);
                PropertyValuesHolder p = PropertyValuesHolder.ofKeyframe("scaleX", k0, k1, k2);
                PropertyValuesHolder p2 = PropertyValuesHolder.ofKeyframe("scaleY", k0, k1, k2);
                ObjectAnimator objectAnimator = ObjectAnimator.ofPropertyValuesHolder(btn_property_anim3, p, p2);
                objectAnimator.setDuration(3000);
                //objectAnimator.setRepeatMode(ValueAnimator.REVERSE);
                //objectAnimator.setRepeatCount(2);
                objectAnimator.start();

                /*AnimatorSet animSet = new AnimatorSet();
                ObjectAnimator transYFirstAnim = ObjectAnimator.ofFloat(btn_property_anim3, "translationY", 0, 100);
                ObjectAnimator transYSecondAnim = ObjectAnimator.ofFloat(btn_property_anim3, "translationY", 100, 0);
                animSet.playSequentially(transYFirstAnim, transYSecondAnim);*/
            }
        });
    }


    private static void insertText(Context context, String text) {
        ContentResolver resolver = context.getContentResolver();
        ContentValues values = new ContentValues();
        values.put("TEXT", text);
        values.put("IS_COLLECT", 0);
        values.put("TIME", String.valueOf(System.currentTimeMillis()));
        try {
            resolver.insert(Uri.parse("content://com.clip.book.info.provider/CLIP_INFO"), values);
        } catch (Exception e) {
            Logs.i("failed to insert cliptext");
        }
    }

    private Handler mClipBookHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            String msgStr = (String) msg.obj;
            ///ClipBookInject.logV("pasteClipBookContent:" + obj);
            Logs.i("msgStr:" + msgStr);
        }
    };


    public void showClipBook(Context context, Handler handler) {
        mContext = context;
        mClipBookHandler = handler;
        Intent intent = new Intent();
        ComponentName componentName = new ComponentName(CLIPBOOK_PKG_NAME, CLIPBOOK_SERVICE_NAME);
        intent.setComponent(componentName);
        context.bindService(intent, mConnection, Context.BIND_AUTO_CREATE);
        Logs.i("bindService then showClipBook() for messenger");
    }


    private Messenger mService = null;
    private final String IPC_MSG_KEY = "ipc_msg_key";
    private static final int TEXT_ITEM_CLICK_EVENT = 1;
    private static final int SHOW_CLIP_BOOK_UI_EVENT = 2;
    private static final int HIDE_CLIP_BOOK_UI_EVENT = 3;
    private final String CLIPBOOK_PKG_NAME = "com.clip.book";
    private final String CLIPBOOK_SERVICE_NAME = "com.clip.book.ClipBookService";
    private final Messenger mMessenger = new Messenger(new IncomingHandler());
    private boolean mIsBound;

    private void doUnbindService() {
        if (mIsBound) {
            mContext.unbindService(mConnection);
            mIsBound = false;
        }
    }

    private class IncomingHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case HIDE_CLIP_BOOK_UI_EVENT:
                    Logs.i("HIDE_CLIP_BOOK_UI_EVENT doUnbindService()");
                    doUnbindService();
                    break;
                case TEXT_ITEM_CLICK_EVENT:
                    Logs.i("receive text from ClipBook");
                    Bundle data = msg.getData();
                    String msg_from_server = data.getString(IPC_MSG_KEY);
                    if (!TextUtils.isEmpty(msg_from_server)) {
                        Message obtainMessage = mHandler.obtainMessage();
                        obtainMessage.obj = msg_from_server;
                        mHandler.sendMessage(obtainMessage);
                    }
                    break;
                default:
                    super.handleMessage(msg);
            }
        }
    }

    private ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className, IBinder service) {
            Logs.i("onServiceConnected---showClipBook()");
            mService = new Messenger(service);
            try {
                Message msg = Message.obtain(null, SHOW_CLIP_BOOK_UI_EVENT);
                msg.replyTo = mMessenger;
                mService.send(msg);
            } catch (RemoteException e) {
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName className) {
            Logs.i("Service Disconnected");
            mService = null;
        }
    };


    /**
     * when phone is not root,we can copy databases from /data/data/packageName/databases/xxx.db to /storage/emulated/0/copy.db by
     * this method.
     * <p>
     * then copy copy.db to PC folder by copy_un_root_phone_db.bat.
     * <p>
     * <p>below code show how to use,it is good to be placed in onResume which will run regularly:
     * Handler handler = new Handler() {
     *
     * @Override public void handleMessage(Message msg) {
     * super.handleMessage(msg);
     * copyOutDatabase();
     * }
     * };
     * <p>
     * //for fear of effect the logic of app,so we delay some time to copy databases.
     * handler.sendEmptyMessageDelayed(0, 5000);
     */
    private void copyOutDatabase() {
        //找到文件的路径  /data/data/包名/databases/数据库名称
        String packageName = "com.android.launcher3";
        //String packageName = getPackageName();
        String path = Environment.getDataDirectory().getAbsolutePath() + "/data/" + packageName + "/databases/launcher.db";
        File dbFile = new File(path);
        FileInputStream fis = null;
        FileOutputStream fos = null;
        try {
            //文件复制到sd卡中
            fis = new FileInputStream(dbFile);
            fos = new FileOutputStream(Environment.getExternalStorageDirectory().getAbsolutePath() + "/copy.db");
            int len = 0;
            byte[] buffer = new byte[2048];
            while (-1 != (len = fis.read(buffer))) {
                fos.write(buffer, 0, len);
            }
            fos.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //关闭数据流
            try {
                if (fos != null) {
                    fos.close();
                }
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }

    private void startVoice() {
        Logs.i();
        try {
            final SearchManager searchManager = (SearchManager) getSystemService(Context.SEARCH_SERVICE);
            ComponentName activityName = searchManager.getGlobalSearchActivity();
            Intent intent = new Intent(RecognizerIntent.ACTION_WEB_SEARCH);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (activityName != null) {
                intent.setPackage(activityName.getPackageName());
            }
            //
            //startActivity(null, intent, "onClickVoiceButton");
            startActivity(intent);
        } catch (ActivityNotFoundException e) {
            Intent intent = new Intent(RecognizerIntent.ACTION_WEB_SEARCH);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            //startActivitySafely(null, intent, "onClickVoiceButton");
            startActivity(intent);
        }
    }

    private void copyDBToSDcard() {
        String DATABASE_NAME = "数据库文件名称";
        String oldPath = "data/data/com.packagename/databases/" + DATABASE_NAME;
        String newPath = Environment.getExternalStorageDirectory() + File.separator + DATABASE_NAME;
        copyFile(oldPath, newPath);
    }

    /**
     * 复制单个文件
     *
     * @param oldPath String 原文件路径
     * @param newPath String 复制后路径
     * @return boolean
     */
    public static void copyFile(String oldPath, String newPath) {
        try {
            int bytesum = 0;
            int byteread = 0;
            File oldfile = new File(oldPath);
            File newfile = new File(newPath);
            if (!newfile.exists()) {
                newfile.createNewFile();
            }
            //文件存在时
            if (oldfile.exists()) {
                // 读入原文件
                InputStream inStream = new FileInputStream(oldPath);
                FileOutputStream fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                while ((byteread = inStream.read(buffer)) != -1) {
                    bytesum += byteread; // 字节数 文件大小
                    fs.write(buffer, 0, byteread);
                }
                inStream.close();
            }
        } catch (Exception e) {
            System.out.println("复制单个文件操作出错");
            e.printStackTrace();

        }
    }

    private void readSystem() {
        File root = Environment.getRootDirectory();
        StatFs sf = new StatFs(root.getPath());
        long blockSize = sf.getBlockSize();
        long blockTotalCount = sf.getBlockCount();
        long availCount = sf.getAvailableBlocks();
        String avail = SizeConverter.BTrim.convert((float) (blockSize * availCount));
        Log.d(TAG, "phone avail:" + avail);
        String used = SizeConverter.BTrim.convert((float) (blockTotalCount - availCount) * blockSize);
        Log.d(TAG, "phone used:" + used);
    }


    private void readSDCard() {
        String state = Environment.getExternalStorageState();
        if (Environment.MEDIA_MOUNTED.equals(state)) {
            File sdcardDir = Environment.getExternalStorageDirectory();
            StatFs sf = new StatFs(sdcardDir.getPath());
            long blockSize = sf.getBlockSize();
            long blockTotalCount = sf.getBlockCount();
            long availCount = sf.getAvailableBlocks();
            ///Log.d("", "block大小:" + blockSize + ",block数目:" + blockTotalCount + ",总大小:" + blockSize * blockTotalCount / 1024 + "KB");
            ///Log.d("", "可用的block数目：:" + availCount + ",剩余空间:" + availCount * blockSize / 1024 + "KB");

            String avail = SizeConverter.BTrim.convert((float) (blockSize * availCount));
            Log.d(TAG, "sd avail:" + avail);
            String used = SizeConverter.BTrim.convert((float) (blockTotalCount - availCount) * blockSize);
            Log.d(TAG, "sd used:" + used);
        }
    }

    /**
     * @todo 获取当前最顶层，正在运行的activity;可以在后台跑个线程，实时的获取当前最顶层显示的包名、类名；还需要继续完善；
     */
    private void getTopApk() {
        ActivityManager amService = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
        ComponentName cn = amService.getRunningTasks(Integer.MAX_VALUE).get(0).topActivity;
        String packageName = cn.getPackageName();
        Log.i(TAG, "packageName:" + packageName);
    }

    /**
     * @todo 执行顺序为：a、b、c、d；handleMessage中的代码，会在当前testHandler方法执行完成之后，才会执行。
     * <AUTHOR>
     * @time 2017/2/17 17:40
     */
    private void testHandler() {
        Logger.e("a");
        mClipBookHandler.sendEmptyMessage(1);
        try {
            Thread.sleep(525);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        Logger.e("b");
    }

    /**
     * @todo 测试logger日志神器，功能非常的强大，输出日志可视化，以及代码的调用栈顺序。
     * <AUTHOR>
     * @time 2017/2/17 16:54
     */
    private void initLogger() {
        Logger.init(TAG)              // 默认为PRETTYLOGGER，可以设置成为自定义tag
                .methodCount(3)             // logger所在方法显示开关 0 为不显示，1、2、3、4、5 等等，分别为显示1至5个方法的嵌套调用
                //.hideThreadInfo()              // 线程信息显示，默认打开
                .logLevel(LogLevel.FULL)    // 默认是打开日志显示（FULL），关闭（NONE）
                .methodOffset(1);           // 默认为0 ,方法体样式：1、2
    }

    /**
     * @todo 跳转到自定义的设置界面
     * <AUTHOR>
     * @time 2017/2/17 14:30
     */
    private void testPreferenceActivity() {
        Intent i = new Intent(this, PreferencesActivity.class);
        startActivity(i);
    }

    /**
     * @todo replace字符串a中的内容时，并不会修改原来的字符串a
     * <AUTHOR>
     * @time 2017/2/17 16:56
     */
    private void testStringReplace() {
        String a = "ad da fdasdf ad dasfaf";
        String replace = a.replace(" ", "");
        Logs.i(TAG, "a:" + a);
        Logs.i(TAG, "replace:" + replace);
    }

    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case 1:
                    Logger.e("c");
                    /*try {
                        Thread.sleep(525);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }*/
                    Logger.e("d");
                    break;
            }
        }
    };

    private void testIsAppInstalled(Context context) {
        boolean pkgInstalled = PackageUtils.isPkgInstalled(context, "com.tencent.mm");
        Logger.v(TAG, "pkgInstalled:" + pkgInstalled);
    }


    /**
     * @todo 初始化简短的声音文件
     * <AUTHOR>
     * @time 2017/4/2 18:49
     */
    private void initSoundPlay() {
        mSoundUtils = new SoundUtils(this);
        mSoundUtils.initSound(this);
    }


    private void testEncrypt() {
        String password = "my_password";
        String salt;
        String ciphertext;
        try {
            salt = PasswordEncryption.generateSalt();
            Logs.i(TAG, "password:" + password);
            Logs.i(TAG, "salt:" + salt);
            ciphertext = PasswordEncryption.getEncryptedPassword(password, salt);
            Logs.i(TAG, "ciphertext:" + ciphertext);
            boolean result = PasswordEncryption.authenticate(password, ciphertext, salt);
            Logs.i(TAG, "result:" + (result ? "succeed" : "failed"));
        } catch (NoSuchAlgorithmException e) {
            System.out.println("NoSuchAlgorithmException");
        } catch (InvalidKeySpecException e) {
            System.out.println("InvalidKeySpecException");
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        // Activity窗口获得/失去焦点时被调用（在onResume之后获得焦点、onPause之后失去焦点）
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            //testHandler();


            testFrameAnimation();
        }
    }

    /**
     * 如果把该方法放在onCreate中，这个时候我们运行一下，发现动画没有运行而是停留在第一帧，那是因为AnimationDrawable播放动画是依附在window上面的，
     * 而在Activity onCreate方法中调用时Window还未初始化完毕，所有才会停留在第一帧，要想实现播放,
     * 必须在onWindowFocusChanged中添加动画的启动代码。
     */
    private void testFrameAnimation() {
        final AnimationDrawable animationDrawable = new AnimationDrawable();
        //方法一，把一张一张图片，直接实例化AnimationDrawable对象
        for (int i = 0; i < 5; i++) {
            int drawableId = getResources().getIdentifier("ic_heart_" + i * 25, "drawable", getPackageName());
            Drawable drawable = getResources().getDrawable(drawableId);
            animationDrawable.addFrame(drawable, 200);
        }
        //方法二，也可以直接获取anim_heart_filling动画文件
        //animationDrawable = (AnimationDrawable) getResources().getDrawable(R.drawable.anim_heart_filling);
        //方法三，直接把动画文件赋值给imageview组件
        //iv_anim_selector.setBackgroundResource(R.drawable.anim_heart_filling);
        animationDrawable.setOneShot(false);
        iv_anim_selector.setImageDrawable(animationDrawable);
        iv_anim_selector.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //iv_anim_selector.setActivated(!iv_anim_selector.isActivated());
                if (animationDrawable != null) {
                    if (animationDrawable.isRunning()) {
                        animationDrawable.stop();
                    } else {
                        animationDrawable.start();
                    }
                }
            }
        });
    }

    @Override
    protected void onStop() {
        Logs.i();
        super.onStop();
    }

    @Override
    protected void onPause() {
        super.onPause();
        Logs.i();
        //有可能在执行完onPause或onStop后,系统资源紧张将Activity杀死,所以有必要在此保存持久数据
    }

    @Override
    public void onLowMemory() {
        /*onLowMemory 当后台程序，即使终止资源还匮乏时会调用这个方法。好的应用程序一般会在这个方法里面释放一些不必
        要的资源来应付当后台程序已经终止，前台应用程序内存还不够时的情况。*/
        super.onLowMemory();
    }

    private LocationManager locationManager;

    @Override
    public void onLocationChanged(Location location) {

    }

    @Override
    public void onStatusChanged(String s, int i, Bundle bundle) {
        Logs.i();
    }

    @Override
    public void onProviderDisabled(String s) {
        Logs.i();
    }

    @Override
    public void onProviderEnabled(String s) {
        Logs.i();
    }

    @Override
    protected void onDestroy() {
        /*RefWatcher refWatcher = MyApplication.getRefWatcher(this);
        refWatcher.watch(this);*/
        Logs.i();
        super.onDestroy();
        finish();
    }

    @Override
    public void onBackPressed() {
        //super.onBackPressed();
        /// 效果同按下HOME按键
        this.moveTaskToBack(true);
        Logs.i();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        int param = 1;
        //存储数据
        outState.putInt("param", param);
        Log.i(TAG, "setParam: " + param);
        /**
         * Activity被系统杀死时被调用.
         * 例如:屏幕方向改变时,Activity被销毁再重建;当前Activity处于后台,系统资源紧张将其杀死.
         * 另外,当跳转到其他Activity或者按Home键回到主屏时该方法也会被调用,系统是为了保存当前View组件的状态.
         * 在onPause之前被调用.
         */
        Logs.i();
        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        //取出在onSaveInstanceState方法中，存储的数据
        int param = savedInstanceState.getInt("param");
        Logs.i("getParam:" + param);
        Logs.i();
        /**
         * Activity被系统杀死后再重建时被调用.
         * 例如:屏幕方向改变时,Activity被销毁再重建;当前Activity处于后台,系统资源紧张将其杀死,用户又启动该Activity.
         * 这两种情况下onRestoreInstanceState都会被调用,在onStart之后.（由于onResume方法后，用户都可以进行界面的操作了，所以不是在onResume之后调用此方法；）
         */
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Logs.i();
        switch (newConfig.orientation) {
            case Configuration.ORIENTATION_PORTRAIT:
                Logs.i("ORIENTATION_PORTRAIT");
                //设置竖屏布局
                //setContentView(R.layout.orientation_portrait);
                break;
            case Configuration.ORIENTATION_LANDSCAPE:
                Logs.i("ORIENTATION_LANDSCAPE");
                //设置横屏布局
                //setContentView(R.layout.orientation_landscape);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
    }

    public void onClick_android_js(View view) {
        Intent intent = new Intent();
        intent.setClass(this, AndroidJSActivity.class);
        startActivity(intent);
    }
}