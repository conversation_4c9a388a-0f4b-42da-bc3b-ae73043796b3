package com.common.utils;

import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;

import java.util.Locale;


/**
 * Android获取指定语言字符串 - CSDN博客.mhtml
 * <p>
 * LanguageUtil.java
 * 调用方法：
 * int stringId = context.getResources().getIdentifier("app_name", "string", LanguageUtil.APPNAME);
 * String stringByLocale = LanguageUtil.getStringByLocale(this, stringId, "en", "US");
 * <p>
 * //或者，通过如下的方式，获取字符串。
 * //String stringByLocale = LanguageUtil.getStringByLocale(this, R.string.app_name, "en", "US");
 * <p>
 * Logs.i("stringByLocale:" + stringByLocale);
 */
public class LanguageUtil {
    /**
     * 指定应用的包名（好像只能是自己的应用的包名，不能为其他应用的包名，如果可以获取到其他应用里面的字符串数据，那就强大了。还需要继续验证。flagtest）
     */
    public static final String APPNAME = "com.bingo.we.smart";
    //public static final String APPNAME = "com.estrongs.android.pop";
    //public static final String APPNAME = "com.tencent.mm";
    public static final String ERROR_LABEL = "";
    private static final String DEFAULT_COUNTRY = "US";
    private static final String DEFAULT_LANGUAGE = "en";

    /**
     * 获取指定包名，指定国家，指定id的字符串文本
     *
     * @param context
     * @param stringId
     * @param language
     * @param country
     * @return
     */
    public static String getStringByLocale(Context context, int stringId, String language, String country) {
        //遍历，当前所有的 字符串国际化的种类
        /*for (Locale locale : Calendar.getAvailableLocales()) {
            String lang = locale.getLanguage();
            //String coun = locale.getCountryPrivate(locale);
            //String localeStr = country.equals("") ? language : (language + "_" + country);

            //Loger.w("ruijie", "language = " + lang + ", country = " + coun + "," + "localeStr = " + localeStr);
            Logs.i("lang:" + lang);
        }*/

        Resources resources = getApplicationResource(context.getApplicationContext().getPackageManager(), APPNAME, new Locale(language, country));
        if (resources == null) {
            return ERROR_LABEL;
        } else {
            try {
                return resources.getString(stringId);
            } catch (Exception e) {
                return ERROR_LABEL;
            }
        }
    }

    /**
     * 直接获取英文字符串
     *
     * @param context
     * @param stringId
     * @return
     */
    public static String getStringOfEnglish(Context context, int stringId) {
        return getStringByLocale(context, stringId, DEFAULT_LANGUAGE, DEFAULT_COUNTRY);
    }

    private static Resources getApplicationResource(PackageManager pm, String pkgName, Locale l) {
        Resources resourceForApplication = null;
        try {
            resourceForApplication = pm.getResourcesForApplication(pkgName);
            updateResource(resourceForApplication, l);
        } catch (PackageManager.NameNotFoundException e) {

        }
        return resourceForApplication;
    }

    private static void updateResource(Resources resource, Locale l) {
        Configuration config = resource.getConfiguration();
        config.locale = l;
        resource.updateConfiguration(config, null);
    }

}