package com.test.android.file


import java.io.File

/**
 * E:\develop\Studio_WorkPlace\TestAndroid\smartjava\src\main\java\com\test\android\file\BatchUpdateGithubProject.bat
 * BatchUpdateGithubProject.bat will replace the DeleteGitFolder.java.
 */
object DeleteGitFolder {
    private val github_src_folder = "E:\\develop\\src\\github_src"

    @JvmStatic
    fun main(args: Array<String>) {
        deleteTargetFile(File(github_src_folder))
    }

    private fun deleteTargetFile(dir: File): Boolean {
        if (!dir.exists()) {
            return true
        }
        if (dir.isDirectory) {
            val children = dir.listFiles()
            for (child in children) {
                if (child.name.equals(".git") && child.path.contains("_Bingo")) {
                    println(child.path)
                    dir.deleteRecursively()//recursive to delete file in kotlin
                    //return dir.delete()
                } else {
                    deleteTargetFile(child)
                }
            }
        }
        return false
    }
    /*
    public static boolean deleteAllFile(File dir, String fileExcept) {
        if (!dir.exists()) {
            return true;
        }
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteAllFile(new File(dir, children[i]), fileExcept);
                if (!success) {
                    return false;
                }
            }
            return true;
        } else {
            if (SomeUtils.isTextEmpty(fileExcept) || !dir.getName().contains(fileExcept)) {
                return dir.delete();
            }
            return true;
        }
    }
    */
}