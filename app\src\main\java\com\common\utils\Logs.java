package com.common.utils;


import android.text.TextUtils;
import android.util.Log;


public class Logs {
    private static final boolean ENABLE_LOG = true;
    public static boolean ENABLE_DETAIL_LOG;
    public final static String APK_TAG = "LocalSmart";

    public static void i() {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.i(APK_TAG, "");
        } else {
            Log.i(APK_TAG, fileInfo);
        }
    }

    public static void d() {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.d(APK_TAG, "");
        } else {
            Log.d(APK_TAG, fileInfo);
        }
    }

    public static void d(String tag, String msg) {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.d(APK_TAG, tag + " " + msg);
        } else {
            Log.d(APK_TAG, fileInfo + ":" + msg);
        }
    }

    public static void i(String tag, String msg) {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.i(APK_TAG, tag + " " + msg);
        } else {
            Log.i(APK_TAG, fileInfo + ":" + msg);
        }
    }

    public static void i(String msg) {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.i(APK_TAG, msg);
        } else {
            Log.i(APK_TAG, fileInfo + ":" + msg);
        }
    }


    public static void d(String msg) {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.d(APK_TAG, msg);
        } else {
            Log.d(APK_TAG, fileInfo + ":" + msg);
        }
    }


    public static void e(String msg) {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.e(APK_TAG, msg);
        } else {
            Log.e(APK_TAG, fileInfo + ":" + msg);
        }
    }

    public static void e(String tag, String msg) {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.e(APK_TAG, tag + " " + msg);
        } else {
            Log.e(APK_TAG, fileInfo + ":" + msg);
        }
    }

    public static void w(String tag, String msg) {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.w(APK_TAG, tag + " " + msg);
        } else {
            Log.w(APK_TAG, fileInfo + ":" + msg);
        }
    }

    public static void v(String tag, String msg) {
        if (!ENABLE_LOG) {
            return;
        }
        String fileInfo = null;
        if (ENABLE_DETAIL_LOG) {
            StackTraceElement stackTrace = Thread.currentThread().getStackTrace()[3];
            fileInfo = stackTrace.getFileName() + "(" + stackTrace.getLineNumber() + ") " + stackTrace.getMethodName();
        }
        if (TextUtils.isEmpty(fileInfo)) {
            Log.v(APK_TAG, tag + " " + msg);
        } else {
            Log.v(APK_TAG, fileInfo + ":" + msg);
        }
    }

}
