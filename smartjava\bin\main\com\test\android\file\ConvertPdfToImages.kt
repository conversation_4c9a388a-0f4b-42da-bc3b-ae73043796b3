package com.test.android.file

import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.rendering.PDFRenderer
import java.awt.image.BufferedImage
import java.io.File
import javax.imageio.ImageIO

/**
 * 非常棒，把100多页的pdf文件转换为jpg图片都毫无压力
 */
fun convertPdfToImages(pdfFilePath: String, outputDir: String, imageFormat: String = "png") {
    try {
        // 1. Load the PDF document
        val document: PDDocument = PDDocument.load(File(pdfFilePath))

        // 2. Create a PDFRenderer
        val pdfRenderer = PDFRenderer(document)

        // 3. Create the output directory if it doesn't exist
        val outputDirFile = File(outputDir)
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs()
        }


        // 4. Iterate through each page and render it as an image
        for (pageNumber in 0 until document.numberOfPages) {
            val image: BufferedImage = pdfRenderer.renderImageWithDPI(pageNumber, 300f) // Render at 300 DPI

            // 5. Construct the output file path
            val outputFileName = "page_${pageNumber + 1}.$imageFormat"
            val outputFile = File(outputDir, outputFileName)

            // 6. Write the image to the output file
            ImageIO.write(image, imageFormat, outputFile)
            println("Converted page ${pageNumber + 1} to ${outputFile.absolutePath}")
        }

        // 7. Close the document
        document.close()
        println("PDF conversion complete.")

    } catch (e: Exception) {
        e.printStackTrace()
        println("Error during PDF conversion: ${e.message}")
    }
}


fun main() {
    val pdfFilePath = "H:\\FamilyCompany\\Order\\SalesOrder\\BingoWebSiteCustomer\\20250318_Lsc\\几字型支架.pdf"  // Replace with the actual PDF file path
    val outputDirectory = "H:\\FamilyCompany\\Order\\SalesOrder\\BingoWebSiteCustomer\\20250318_Lsc" // Replace with your desired output directory
    val imageFormat = "jpg" // or "png", "gif", etc.

    convertPdfToImages(pdfFilePath, outputDirectory, imageFormat)
}