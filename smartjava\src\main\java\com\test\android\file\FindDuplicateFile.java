package com.test.android.file;


import com.test.android.SomeFileUtils;
import com.test.android.SomeUtils;
import com.test.android.common.CommonStringUtils;
import com.test.android.common.Constants;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.channels.FileChannel;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @todo 找出指定目录中的相同的文件
 */
public class FindDuplicateFile {
    private static final String TAG = "FindDuplicateFile:";
    private static final boolean IS_DELETE_DUPLICATE_FILE = false;
    /**
     * 是否判断名字相同时，才算是重复的文件
     */
    private static final boolean IS_NEED_FILE_NAME_EQUAL = false;

    /**
     * if the size of image is bigger,then the image will not be remained,so the image need to be compared.
     */
    private static final long IMAGE_SIZE_NEED_COMPARE = 15 * Constants.SIZE_KB;
    /**
     * whether remain small image or not
     */
    private static final boolean IS_SMALL_IMAGE_DEFAULT_REMAINED = true;
    //in order to reduce the cost of time and CPU,replace md5 with file size when file is bigger then 1GB.
    private static final boolean BIG_FILE_ONLY_COMPARE_SIZE = true;
    //if true then one file is remained and it do not need to compare.
    private static final boolean IS_FILE_DEFAULT_REMAINED = true;
    /**
     * if the size of file is smaller,then do not need compare.
     */
    private static long FILE_SIZE_TO_REMAIN = 1 * Constants.SIZE_MB;
    private static Map<String, String> md5Map = new HashMap<>();
    private static Map<String, List<String>> duplicateMap = new HashMap<>();
    private static ArrayList<String> remainList = new ArrayList<String>() {
        {
            // 屏蔽一些安装目录、sdk目录、jdk目录等
            add("E:\\develop_software\\android-sdk");
            add("E:\\develop_software\\Java");
            add("E:\\software_installed");
            add("F:\\software_installed");
            add("F:\\develop\\src");
            add("CP_PrivacySpace");


            //屏蔽工程里面的文件
            add("apmserv");
            add("APMServ5");
            add("\\res\\");
            add(".java");
            add(".xml");
            add("\\src\\");
        }
    };
    private static ArrayList<String> compareList = new ArrayList<String>() {
        {
            // 匹配安装包
            add(".apk");
            add(".exe");
            add(".iso");

            //匹配压缩包
            add(".rar");
            add(".zip");

            //filter web file
            add(".mhtml");

            // 匹配mp3文件
            add(".mp3");
            add(".flac");
            //add(".lrc");
            add(".aac");

            //匹配视频文件
            add(".mp4");
            add(".3gp");
            add(".avi");
            add(".mov");
            add(".mpeg");
            add(".mpg");
            add(".asf");
            add(".wmv");
            add(".mkv");

            //匹配文本文档
            /*add(".txt");
            add(".doc");
            add(".docx");
            add(".ppt");
            add(".xlsx");
            add(".chm");*/
        }
    };

    public static void main(String[] args) {
        addTwoNumbers();

        SimpleDateFormat dateFormat = new SimpleDateFormat("YYYYMMdd_HHmmss");
        String startDate = dateFormat.format(new Date());
        SomeUtils.printLogToFile("H:\\delete_duplicate_file_log\\delete_duplicate_file_log_" + startDate + ".txt");
        //in order to write log into txt file,this line code should be placed after SomeUtils.printLogToFile.
        System.out.println(TAG + "startDate:" + startDate);
        // 对于磁盘根目录
        // String dir = "E:/";
        // String dir = "H:/AutoSmart/hk/weimi";
        //String dir = "H:/AutoSmart";
        //String dir = "H:/AutoSmart/jq";
        String dir = "H:/AutoSmart/zj/myapk_file";
        //String dir = "H:/AutoSmart/cy/TaoShop";
        //String dir = "H:/zj";
        //String dir = "H:/AutoSmart/ym";
        //String dir = "H:/zj";
        //String dir = "H:/AutoSmart/ym";
        // String dir = "E:/360Downloads";
        findAndDeleteDuplicateFile(dir);
        // findFileNotOnRightPlace(dir);
        String endDate = dateFormat.format(new Date());
        System.out.println(TAG + "endDate:" + endDate);
        //delayToClosePC();

        //CommonFileUtils.deleteEmptyFolder(dir);

        /*boolean isContain = isContainCopyName2(new File("G:\\develop\\doc\\360osui规范及公共控件\\动效与控件2.0\\通用控件\\下拉刷新\\下拉刷新\\下拉刷新1 - 副本" +
                ".png"), "副本");*/
    }


    private static String mSumStr;
    private static double node1sum;
    private static double node2sum;

    public static ListNode addTwoNumbers(/*ListNode l1, ListNode l2*/) {
        /*ListNode node1 = new ListNode(0);
        ListNode node2 = new ListNode(0);*/
        ListNode node1 = new ListNode(1);
        /*node1.next = new ListNode(4);
        node1.next.next = new ListNode(3);*/
        /*node1.next.next.next = new ListNode(9);
        node1.next.next.next.next = new ListNode(9);*/

        ListNode node2 = new ListNode(1);
        node2.next = new ListNode(9);
        node2.next.next = new ListNode(9);
        node2.next.next.next = new ListNode(9);
        node2.next.next.next.next = new ListNode(9);
        node2.next.next.next.next.next = new ListNode(9);
        node2.next.next.next.next.next.next = new ListNode(9);
        node2.next.next.next.next.next.next.next = new ListNode(9);
        node2.next.next.next.next.next.next.next.next = new ListNode(9);
        node2.next.next.next.next.next.next.next.next.next = new ListNode(9);

        getListNode1Next(node1);
        classType = 0;
        getListNode2Next(node2);
        double sum = Math.round(node1sum + node2sum);
        mSumStr = String.valueOf(sum);
        if (mSumStr.contains(".")) {
            String[] splitStr = mSumStr.split(".");
            if (splitStr != null && splitStr.length > 0) {
                mSumStr = splitStr[0];
            }
        }
        ListNode node3 = new ListNode(Integer.parseInt(String.valueOf(mSumStr.charAt(mSumStr.length() - 1))));
        if (mSumStr.length() > 1) {
            node3.next = new ListNode(0);
            setListNodeNext(node3, mSumStr.length() - 2);
        }
        return node3;
    }

    public static int classType = 0;

    public static void getListNode1Next(ListNode item) {
        node1sum = node1sum + (long) (item.val * Math.pow(10, classType));
        if (item.next == null) {
        } else {
            classType++;
            getListNode1Next(item.next);
        }
    }

    public static void getListNode2Next(ListNode item) {
        node2sum = node2sum + (double) (item.val * Math.pow(10, classType));
        if (item.next == null) {
        } else {
            classType++;
            getListNode2Next(item.next);
        }
    }

    public static ListNode setListNodeNext(ListNode item, int index) {
        char c = mSumStr.charAt(index);
        String s = String.valueOf(c);
        item.next.val = Integer.parseInt(s);

        index--;
        if (index < 0) {
            return item;
        } else {
            item.next.next = new ListNode(0);
            setListNodeNext(item.next, index);
        }
        return null;
    }

    public static class ListNode {
        int val;
        ListNode next;

        ListNode(int x) {
            val = x;
        }
    }


    /**
     * @todo close the computer
     * <AUTHOR>
     * @time 2017/9/9 23:53
     */
    private static void delayToClosePC() {
        try {
            Thread.sleep(65000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        try {
            Runtime.getRuntime().exec("Shutdown -s");
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        System.out.println("close PC one");
        System.out.println();
    }

    /**
     * @todo 在指定的目录中，查找重复的文件，并删除
     */
    private static void findAndDeleteDuplicateFile(String dir) {
        try {
            findDuplicateFileList(dir);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (IS_DELETE_DUPLICATE_FILE) {
            deleteDuplicateFile(duplicateMap);
        }
    }

    /**
     * @todo 临时自定义需求，查找位置没有放置正确的文件；
     * <p>
     * 此功能主要针对，jq文件夹中的不同类别的文件，分类进行放置（DoWithChrome、DoWithAndroidStudio）
     */
    private static void findFileNotOnRightPlace(String dir) {
        long time1 = System.currentTimeMillis();
        Collection<File> files = (Collection<File>) FileUtils.listFiles(new File(dir), null, true);
        System.out.println("listFile---diff:" + (System.currentTimeMillis() - time1) + "ms");
        for (File file : files) {
            String absolutePath = file.getAbsolutePath();
            if (absolutePath == null || "".equals(absolutePath)) {
                return;
            }
            String FOLDER_NAME = "DoWithChrome";
            String FOLDER_NAME_FLAG = "Chrome";

            if (absolutePath.contains(FOLDER_NAME_FLAG) && !absolutePath.contains(FOLDER_NAME)) {
                String newFilePath = file.getParent() + "\\" + FOLDER_NAME + "\\" + file.getName();
                boolean copyFile = SomeFileUtils.copySingleFile(absolutePath, newFilePath);
                if (copyFile) {
                    removeOccupyFile();
                    boolean delete = file.getAbsoluteFile().delete();
                    if (delete) {
                        System.out.println(absolutePath + " moved:true");
                    } else {
                        System.out.println(absolutePath + " moved:false");
                    }
                } else {
                    System.out.println(absolutePath + " moved:false");
                }
            }
        }
    }


    /**
     * 解除程序对文件的占用，以便后续正常的删除文件
     */
    private static void removeOccupyFile() {
        System.gc();
        try {
            Thread.sleep(58);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private static void deleteDuplicateFile(Map<String, List<String>> duplicateMap) {
        System.out.println();
        final int remainFileIndex = 1;
        if (duplicateMap == null || duplicateMap.size() == 0) {
            System.out.println("none file is duplicate");
            return;
        }
        removeOccupyFile();
        int deleteCount = 0;
        for (List<String> duplicateFileList : duplicateMap.values()) {
            if (duplicateFileList == null || duplicateFileList.size() <= 1) {
                continue;
            }
            String firstFilePath = duplicateFileList.get(0);
            File firstFile = new File(firstFilePath);
            for (int i = remainFileIndex; i < duplicateFileList.size(); i++) {
                String filePath = duplicateFileList.get(i);
                /*if (filePath.contains("MicroMsg") || filePath.contains("wx_video") || filePath.contains("adb_file") || filePath
                        .contains("Screenshots") || filePath.contains("副本") || filePath.contains("(2)")) {*/
                File file = new File(filePath);
                /*if (file.getAbsolutePath().contains("（2）")
                        || firstFile.getAbsolutePath().contains("（2）")
                        || file.getAbsolutePath().contains("(2)")
                        || firstFile.getAbsolutePath().contains("(2)")
                        || isContainCopyName(file, "\\u526f\\u672c")//the unicode of "副本"
                        || isContainCopyName(firstFile, "\\u526f\\u672c")
                        || file.getParentFile().getPath().equalsIgnoreCase(firstFile.getParentFile().getPath())
                        || file.getName().contains(firstFile.getName())
                        || firstFile.getName().contains(file.getName())) {*/
                //boolean delete = file.getAbsoluteFile().delete();
                boolean delete = file.delete();
                System.out.println(file.getAbsolutePath() + " delete:" + String.valueOf(delete));
                deleteCount++;
                if (deleteCount == 5) {
                    System.out.println("the count of deleted files:" + deleteCount);
                    return;
                }
                //}
            }
        }
        System.out.println("the count of deleted files:" + deleteCount);
    }

    /**
     * whether filename contains txtUnicode(been converted from string) by compare two unicode.
     * <p>
     * * We should get the unicode of Chinese Words by "http://tool.chinaz.com/tools/unicode.aspx" first,then compare two Unicode.
     *
     * @param file
     * @param txtUnicode
     * @return
     */
    private static boolean isContainCopyName(File file, String txtUnicode) {
        String absolutePath = file.getAbsolutePath();
        String pathUnicode = CommonStringUtils.getUnicode(absolutePath);
        //System.out.println("pathUnicode" + pathUnicode);
        if (pathUnicode.contains(txtUnicode)) {
            return true;
        }
        return false;
    }


    private static boolean isContainCopyName2(File file, String txtName) {
        try {
            String absolutePath = file.getAbsolutePath();
            //String pathUnicode = CommonStringUtils.getUnicode(absolutePath);
            txtName = new String(txtName.getBytes("8859_1"), "GB2312");
            //System.out.println("pathUnicode" + pathUnicode);
            if (absolutePath.contains(txtName)) {
                return true;
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return false;
    }

    private static void findDuplicateFileList(String dir) throws Exception {
        long time1 = System.currentTimeMillis();
        Collection<File> files = (Collection<File>) FileUtils.listFiles(new File(dir), null, true);
        System.out.println(TAG + "listFile---diff:" + (System.currentTimeMillis() - time1) + "ms");
        System.out.println(TAG + "listfile count is:" + files.size());
        for (File file : files) {
            //String absolutePath = file.getAbsolutePath();
            if (!isRemainFile(file)) {
                System.out.println(file.getPath());
                compare(file);
            }
        }
        System.out.println(TAG + "the count of files which has duplicate files:" + duplicateMap.size());
        int totalDuplicateFileNum = 0;
        for (List<String> duplicateFileList : duplicateMap.values()) {
            System.out.println("duplicateFileList:" + duplicateFileList);
            totalDuplicateFileNum += duplicateFileList.size();
        }
        System.out.println(TAG + "the count of the same files:" + (totalDuplicateFileNum - duplicateMap.size()));
    }

    /**
     * @param absolutePath
     * @return true 表示需要保留，不再进行比对；false  表示不保留，则需要进行比对；
     * @todo 指定哪些文件需要保留，需要保留的文件则不再进行比对
     */
    private static boolean isRemainFile(File file) {
        String absolutePath = file.getAbsolutePath();
        // testMethod(absolutePath);
        if (absolutePath == null || "".equalsIgnoreCase(absolutePath)) {
            return true;
        }
        /*if (file.length() <= FILE_SIZE_TO_REMAIN) {
            return true;
        }*/
        //this condition must check first of all
        for (String item : remainList) {
            if (absolutePath.contains(item)) {
                return true;
            }
        }

        // 过滤图片文件
        if (absolutePath.endsWith(".jpg") || absolutePath.endsWith(".png") || absolutePath.endsWith(".bmp") || absolutePath.endsWith(".gif") ||
                absolutePath.endsWith(".jpeg") || absolutePath.endsWith(".psd")) {
            if (file.length() >= IMAGE_SIZE_NEED_COMPARE) {
                //do not be remained,so the image need to be compared.
                return false;
            } else {
                return IS_SMALL_IMAGE_DEFAULT_REMAINED;
            }
        }
        for (String item : compareList) {
            if (absolutePath.endsWith(item) || absolutePath.endsWith(item.toUpperCase())) {
                return false;
            }
        }
        return IS_FILE_DEFAULT_REMAINED;
    }

    public static void compare(File file) throws Exception {
        String filePath = file.getAbsolutePath();
        String md5 = getFileMd5(filePath);
        if (md5 == null || "".equalsIgnoreCase(md5)) {
            return;
        }
        if (md5Map.keySet().contains(md5)) {
            if (isNameEqual(md5, filePath)) {
                List<String> fileNameList = duplicateMap.get(md5);
                if (fileNameList == null) {
                    fileNameList = new ArrayList<String>();
                    // 加入第一个
                    fileNameList.add(md5Map.get(md5));
                }
                fileNameList.add(filePath);
                duplicateMap.put(md5, fileNameList);
            }
        } else {
            md5Map.put(md5, filePath);
        }
    }

    /**
     * @param md5
     * @param filePath
     * @return
     * @todo 判断文件名字是否相同
     * <AUTHOR>
     * @date 2017年1月7日 下午6:18:31
     */
    private static boolean isNameEqual(String md5, String filePath) {
        // 当 IS_NEED_FILE_NAME_EQUAL=true 时，才进行名字的比对；否则默认认为名字相同；
        if (!IS_NEED_FILE_NAME_EQUAL) {
            return true;
        }
        String firstFilePath = md5Map.get(md5);
        String firstFileName = SomeFileUtils.getFileName(firstFilePath);
        String secondFileName = SomeFileUtils.getFileName(filePath);
        if (SomeUtils.isTextEmpty(firstFileName) || SomeUtils.isTextEmpty(secondFileName)) {
            return false;
        }
        if (firstFileName.equalsIgnoreCase(secondFileName)) {
            return true;
        }
        return false;
    }

    public static String getFileMd5(String filePath) {
        String md5;
        FileInputStream is = null;
        FileChannel fc = null;
        try {
            is = new FileInputStream(new File(filePath));
            fc = is.getChannel();
            long size = fc.size();
            if (size < 158 * Constants.SIZE_MB) {
                md5 = SomeFileUtils.getMd5OfSmallFile1(new File(filePath));
            } else {
                if (BIG_FILE_ONLY_COMPARE_SIZE) {
                    md5 = String.valueOf(new File(filePath).length());
                } else {
                    md5 = SomeFileUtils.getMd5OfLargeFile(filePath);
                }
            }
            //System.out.println(file + "计算MD5时间:\t" + (System.currentTimeMillis() - fromTime));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return null;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                is.close();
                fc.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return md5;
    }
}