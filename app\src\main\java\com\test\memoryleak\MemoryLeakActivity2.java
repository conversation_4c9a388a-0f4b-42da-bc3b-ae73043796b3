package com.test.memoryleak;

import android.app.Activity;
import android.os.AsyncTask;
import android.os.Bundle;
import android.widget.TextView;

import com.test.android.R;

public class MemoryLeakActivity2 extends Activity {
    TextView textView;
    AsyncTask task;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.memory_leak_layout);
        textView = (TextView) findViewById(R.id.text_view);
        task = new BackgroundTask(textView).execute();
    }

    @Override
    protected void onDestroy() {
        /**
         * cancel 任务
         */
        task.cancel(true);
        super.onDestroy();
    }

    /**
     * 采用静态内部类的形式
     */
    private static class BackgroundTask extends AsyncTask<Void, Void, String> {
        private final TextView resultTextView;
        public BackgroundTask(TextView resultTextView) {
            this.resultTextView = resultTextView;
        }

        @Override
        protected void onCancelled() {
            // Cancel mAsyncTask. Code omitted.
        }

        @Override
        protected String doInBackground(Void... params) {
            // Do background work. Code omitted.

            return "some string";
        }

        @Override
        protected void onPostExecute(String result) {
            resultTextView.setText(result);
        }
    }
}
