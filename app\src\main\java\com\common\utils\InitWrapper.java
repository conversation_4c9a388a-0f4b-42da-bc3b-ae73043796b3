package com.common.utils;


import android.content.Context;
import android.content.pm.ApplicationInfo;

import com.orhanobut.logger.LogLevel;
import com.orhanobut.logger.Logger;

public class InitWrapper {
    /**
     * init the follow items:
     * <p>
     * 1.whether show detail log:ENABLE_DETAIL_LOG
     * <p>
     * 2.whether open Strict Mode.
     *
     * @param context
     */
    public static void init(Context context) {
        ApplicationInfo applicationInfo = context.getApplicationInfo();
        /**
         * 但是当我们没在AndroidManifest.xml中设置其debug属性时:
         * 使用Eclipse运行这种方式打包时其debug属性为true,使用Eclipse导出这种方式打包时其debug属性为法false.
         * 在使用ant打包时，其值就取决于ant的打包参数是release还是debug.
         * 因此在AndroidMainifest.xml中最好不设置android:debuggable属性置，而是由打包方式来决定其值.
         */
        int versionType = applicationInfo.flags & ApplicationInfo.FLAG_DEBUGGABLE;
        final int RELEASE_VERSION = 0;
        if (versionType == RELEASE_VERSION) {
            Logs.ENABLE_DETAIL_LOG = false;
            Logs.i("RELEASE_VERSION");
            //do something1 for release version
        } else {
            Logs.ENABLE_DETAIL_LOG = true;
            Logs.i("DEBUG_VERSION");
            initLogger();
            //do something2 for debug version
            //StrictModeWrapper.initStrictMode();
        }
        DeviceAppUtils.logAPPVersionInfo(context);
    }


    /**
     * @todo 测试logger日志神器，功能非常的强大，输出日志可视化，以及代码的调用栈顺序。
     * <AUTHOR>
     * @time 2017/2/17 16:54
     */
    private static void initLogger() {
        Logger.init(Logs.APK_TAG)              // 默认为PRETTYLOGGER，可以设置成为自定义tag
                .methodCount(3)             // logger所在方法显示开关 0 为不显示，1、2、3、4、5 等等，分别为显示1至5个方法的嵌套调用
                //.hideThreadInfo()              // 线程信息显示，默认打开
                .logLevel(LogLevel.FULL)    // 默认是打开日志显示（FULL），关闭（NONE）
                .methodOffset(1);           // 默认为0 ,方法体样式：1、2
    }


}
