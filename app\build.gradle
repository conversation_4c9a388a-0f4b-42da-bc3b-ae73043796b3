import java.text.SimpleDateFormat

plugins {
    id 'com.android.application'
}

ext.firstNum = 1
ext.secondNum = 10
ext.thirdNum = 102
android {
    compileSdk 31
    defaultConfig {
        applicationId "com.test.android"
        minSdk 21
        targetSdk 31
        versionCode generateVersionCode()
        versionName generateVersionName()
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true

        sourceSets {
            main {
                //如果.so放在TestAndroid\app\libs目录下，则需要进行如下的配置；
                //如果.so放在TestAndroid\app\src\main\jniLibs=目录下，则不需要进行如下的配置。
                //以app\为根目录的相对路径
                //jniLibs.srcDirs = ['libs']//正式发布时，需要把.so文件，拷贝到app\libs目录下。
                jniLibs.srcDirs = ['build/intermediates/ndk/debug/obj/local']
//直接引用生成目录的.so文件，免得每次都需要拷贝。


                //采用在studio配置命令快捷键的方式时，配置方式如下：(这种方式，没有走通；参考：)
                //jni.srcDirs = [] //禁止gradle 自动编译，使用已经编译好的So库
                //jniLibs.srcDir "src/main/libs"
                //jniLibs.srcDirs = ['src/main/jniLibs','libs']//指向要使用的库文件的路径，前边的是自己项目的，后边的是第三方的so;如果没指定到.so所在目录的话，则打包的APK中不包含.so文件
            }
        }
        ndk {
            moduleName "bingo"  //so的名字
            abiFilters "armeabi", "armeabi-v7a", "x86"
            stl "stlport_static"    //打开.c 的 debug （此句是打开的debug的关键）
        }
    }


    buildTypes {
        debug {
            jniDebuggable true //此句不加在真机上 debug 不受影响，但是在虚拟机上不能 debug
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
        }
    }

    //解决签名报错的问题start
    namespace 'com.test.android'
    lint {
        abortOnError false
        checkReleaseBuilds false
    }
    //解决签名报错的问题end
}


def generateVersionCode() {
    firstNum * 100000 + secondNum * 1000 + thirdNum
}

def generateVersionName() {
    def date = new Date()
    SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss")
    String dateString = formatter.format(date.getTime())

    long tmpTimeStamp = Long.valueOf(dateString)
    //tmpTimeStamp += 12345678123456L
    dateString = String.valueOf(tmpTimeStamp)

    //String today = new Date().format("YYYY.MM.dd")
    //String time = new Date().format("HH:mm:ss")
    firstNum + "." + secondNum + "." + thirdNum + "_VER_" + dateString
}


dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.room:room-runtime-android:2.7.0-beta01'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    testImplementation "junit:junit:4.13.2"


    implementation 'com.android.support:support-annotations:28.0.0'

    implementation 'com.orhanobut:logger:2.2.0'
    //debugImplementation 'com.squareup.leakcanary:leakcanary-android:1.5'
    //releaseImplementation 'com.squareup.leakcanary:leakcanary-android-no-op:1.5'
    //testImplementation 'com.squareup.leakcanary:leakcanary-android-no-op:1.5'
}
