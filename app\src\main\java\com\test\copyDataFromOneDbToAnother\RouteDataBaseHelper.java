package com.test.copyDataFromOneDbToAnother;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

public class RouteDataBaseHelper extends SQLiteOpenHelper {
	public RouteDataBaseHelper(Context context) {
		super(context, "route.db", null, 1);
	}

	private static RouteDataBaseHelper mHelper;

	public static synchronized RouteDataBaseHelper getInstance(Context context) {
		if (mHelper == null) {
			mHelper = new RouteDataBaseHelper(context);
		}
		return mHelper;
	}

	@Override
	public void onCreate(SQLiteDatabase db) {
	}

	@Override
	public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
	}

	public String searchMarchBand(String shortMac) {
		String marchBand = "";
		Cursor cursor = getReadableDatabase().rawQuery("select brand from mac2brand where shortMac=? limit 1", new String[] { shortMac });
		if (cursor.moveToFirst()) {
			marchBand = cursor.getString(0);
		}
		cursor.close();
		return marchBand;
	}

	public int isExistShortMac(String shortMac) {
		int count = 0;
		Cursor cursor = getReadableDatabase().rawQuery("select count(*) from mac2brand where shortMac=?", new String[] { shortMac });
		if (cursor.moveToFirst()) {
			count = cursor.getInt(0);
		}
		cursor.close();
		return count;
	}

	public synchronized void insertRouteInfo(RouteInfoModel routeInfoModel) throws SQLException {
		ContentValues contentValues = new ContentValues();
		contentValues.put("shortMac", routeInfoModel.getMac());
		contentValues.put("brand", routeInfoModel.getBrand());
		final SQLiteDatabase writableDatabase = getWritableDatabase();
		writableDatabase.insertOrThrow("mac2brand", null, contentValues);
	}

	public synchronized int getDbSize() {
		int size = 0;
		Cursor cursor = getReadableDatabase().rawQuery("select count(*) from mac2brand", null);
		if (cursor.moveToFirst()) {
			size = cursor.getInt(0);
		}
		cursor.close();
		return size;
	}

	public synchronized void deleteRouteInfo(String mac, String searchMarchBand) {
		getReadableDatabase().execSQL("delete from mac2brand where shortMac=? and brand=?", new String[] { mac, searchMarchBand });
	}
}