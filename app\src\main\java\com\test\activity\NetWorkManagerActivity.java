package com.test.activity;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;

import com.test.NetworkManager;
import com.test.android.R;

/**
 * <AUTHOR>
 * @todo 获取设备wifi网络，移动网络，飞行模式的状态，并控制wifi网络，移动网络，飞信模式的开关。
 * @date 2014年5月19日 下午12:02:45
 */
public class NetWorkManagerActivity extends Activity implements OnClickListener {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.network_manager);
        controlNetWork();
    }

    /**
     * <AUTHOR>
     * @todo 获取设备wifi网络，移动网络，飞行模式的状态，并控制wifi网络，移动网络，飞信模式的开关。
     * @date 2014年5月18日 下午9:43:07
     */
    private void controlNetWork() {
        Button btn_network_open = (Button) findViewById(R.id.btn_network_open);
        Button btn_network_close = (Button) findViewById(R.id.btn_network_close);
        Button btn_wifi_open = (Button) findViewById(R.id.btn_wifi_open);
        Button btn_wifi_close = (Button) findViewById(R.id.btn_wifi_close);
        Button btn_fly_open = (Button) findViewById(R.id.btn_fly_open);
        Button btn_fly_close = (Button) findViewById(R.id.btn_fly_close);

        // 开关移动网络，需要android.permission.CHANGE_NETWORK_STATE，android.permission.ACCESS_NETWORK_STATE权限.
        btn_network_open.setOnClickListener(this);
        btn_network_close.setOnClickListener(this);
        // 开关WiFi网络，需要android.permission.CHANGE_WIFI_STATE，android.permission.ACCESS_WIFI_STATE权限.
        btn_wifi_open.setOnClickListener(this);
        btn_wifi_close.setOnClickListener(this);
        // 开个飞行模式，需要android.permission.WRITE_SETTINGS权限.
        btn_fly_open.setOnClickListener(this);
        btn_fly_close.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        NetworkManager networkManager = new NetworkManager(this);
        switch (v.getId()) {
            case R.id.btn_network_open:
                try {
                    networkManager.toggleGprs(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            case R.id.btn_network_close:
                try {
                    networkManager.toggleGprs(false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            case R.id.btn_wifi_open:
                networkManager.toggleWiFi(true);
                break;
            case R.id.btn_wifi_close:
                networkManager.toggleWiFi(false);
                break;
            case R.id.btn_fly_open:
                networkManager.toggleAirplaneMode(true);
                break;
            case R.id.btn_fly_close:
                networkManager.toggleAirplaneMode(false);
                break;
            default:
                break;
        }
    }

}
