package com.common.utils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.LineNumberReader;

/**
 * 此方法只适用于电脑ping，在Android测试ping不了
 */
public class PingIpUtil {
    private final String TAG = this.getClass().getSimpleName().trim();
    /// IP
    public String ip;

    public PingIpUtil(String ip) {
        this.ip = ip;
    }

    public void run() {
        try {
            Process p = Runtime.getRuntime().exec("ping " + ip + " -w 300 -n 1");
            InputStreamReader ir = new InputStreamReader(p.getInputStream());
            LineNumberReader input = new LineNumberReader(ir);
            //读取结果行
            for (int i = 1; i < 7; i++) {
                input.readLine();
            }
            String line = input.readLine();
            /// System.out.println("OK!!"+ip+"--OUT:-->"+line);

            if (line == null || line.length() < 17 || line.substring(8, 17).equals("timed out")) {
                Logs.i(TAG, ip + "-------" + false);
            } else {
                Logs.i(TAG, ip + "-------" + true);
            }
        } catch (IOException e) {
        }
    }
}