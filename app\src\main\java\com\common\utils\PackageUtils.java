package com.common.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Environment;

import java.io.File;


/**
 * <AUTHOR>
 * @todo 应用package相关的工具类
 * @time 2017/4/2 18:51
 */
public class PackageUtils {
    /**
     * @todo 判断某个应用是否安装
     * <AUTHOR>
     * @time 2017/4/2 18:40
     */
    public static boolean isPkgInstalled(Context context, String pkgName) {
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(pkgName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            packageInfo = null;
            e.printStackTrace();
        }
        return packageInfo != null;
    }

    /**
     * 安装指定的APK文件
     *
     * @param context
     * @param apkFilePath
     */
    private void installAPK(Context context, String apkFilePath) {
        File apkfile = new File(Environment.getExternalStorageDirectory().getAbsolutePath() + apkFilePath);
        if (!apkfile.exists()) {
            return;
        }
        Intent i = new Intent(Intent.ACTION_VIEW);
        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        i.setDataAndType(Uri.parse("file://" + apkfile.toString()), "application/vnd.android.package-archive");
        context.startActivity(i);
    }

    /**
     * 提取当前应用的APK
     *
     * @param context
     * @return
     */
    public static String extractApk(Context context) {
        ApplicationInfo applicationInfo = context.getApplicationContext().getApplicationInfo();
        String apkPath = applicationInfo.sourceDir;
        Logs.i("extractApk Path:" + apkPath);
        return apkPath;
    }
}
