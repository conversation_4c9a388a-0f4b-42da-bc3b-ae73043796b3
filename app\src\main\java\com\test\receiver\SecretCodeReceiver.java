package com.test.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.test.activity.SystemSettingActivity;


/*
然后在拨号盘输入*#*#5656#*#*，就能启动对应的activity了


<!--通过拨号盘暗码，拉起指定的activity-->
    <receiver android:name="com.test.receiver.SecretCodeReceiver">
    <intent-filter>
    <action android:name="android.provider.Telephony.SECRET_CODE"/>
    <data
        android:host="5656"
        android:scheme="android_secret_code"/>
    </intent-filter>
    </receiver>
*/
public class SecretCodeReceiver extends BroadcastReceiver {
    private final String SECRET_CODE_ACTION = "android.provider.Telephony.SECRET_CODE";


    public SecretCodeReceiver() {
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent != null && intent.getAction().equalsIgnoreCase(SECRET_CODE_ACTION)) {
            Intent activityIntent = new Intent(context, SystemSettingActivity.class);
            activityIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(activityIntent);
        }
    }
}
