package com.test.android;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

public class OtherTest {

    public static void main(String[] args) {
        //testGame();
        //testRepayList();
        //repayList();

        //everyMonthBackCalc(5000, 6);
        //freeDateBackCalc(5000, "2017-0515", "2017-1015");

        //freeDateNotRule();

        //calcPeriodCount();

        //calcPeriodCount();

        enumeratePhoneUnlockCode();
    }

    /**
     * 枚举手机可能的解锁密码
     */
    private static void enumeratePhoneUnlockCode() {
        int index = 0;
        int[] number = {1, 2, 3, 5, 6, 8, 9};
        for (int i0 = 0; i0 < number.length; i0++) {
            for (int i1 = 0; i1 < number.length; i1++) {
                for (int i2 = 0; i2 < number.length; i2++) {
                    for (int i3 = 0; i3 < number.length; i3++) {
                        int equalNumber = countEqualNumber(number[i0], number[i1], number[i2], number[i3]);
                        if (equalNumber < 3) {
                            System.out.println("index:" + ++index);
                            //System.out.println("equalCount = " + equalNumber);
                            System.out.println(number[i0] + " - " + number[i1] + " - " + number[i2] + " - " + number[i3]);
                            System.out.println();
                        }
                    }
                }
            }
        }
    }

    /**
     * 统计4个数字当中，有多少个数字相同
     *
     * @param i0
     * @param i1
     * @param i2
     * @param i3
     * @return
     */
    private static int countEqualNumber(int i0, int i1, int i2, int i3) {
        int[] nums = {i0, i1, i2, i3};
        int equalCount[] = new int[4];

        for (int m = 0; m < nums.length; m++) {
            for (int n = 0; n < nums.length; n++) {
                if (nums[m] == nums[n]) {
                    equalCount[m]++;
                }
            }
        }

        int max = Math.max(Math.max(equalCount[0], equalCount[1]), Math.max(equalCount[2], equalCount[3]));
        return max == 1 ? 0 : max;
    }

    private static void calcPeriodCount() {
        double moneyOfEachPeriod = 9200 * (0.46 * 0.01 * 12 + 1) / 12;
        System.out.println("moneyOfEachPeriod:" + moneyOfEachPeriod);
    }

    /**
     * 适用于，不规律的随借随还；一般最后一期会出现不规律。
     */
    private static void freeDateNotRule() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MMdd");
        String borrowDateStr = "2017-1213";
        Date borrowDate = null;
        try {
            borrowDate = simpleDateFormat.parse(borrowDateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(borrowDate);
        int borrowDay = calendar.get(Calendar.DAY_OF_YEAR);

        //RepayDate\TheCountMoneyToCalcFee
        LinkedHashMap<String, Integer> repayDateMoneyMap = new LinkedHashMap<>();
        repayDateMoneyMap.put("2018-0121", 20000);
        //repayDateMoneyMap.put("2017-1208", 20000);
        /*repayDateMoneyMap.put("2017-0721", 7800);
        repayDateMoneyMap.put("2017-0821", 7100);
        repayDateMoneyMap.put("2017-0921", 6400);
        repayDateMoneyMap.put("2017-1021", 5700);
        repayDateMoneyMap.put("2017-1106", 5000);*/

        float FEE_RATE = 0.025f * 0.01f;
        Set<String> repayDateStrs = repayDateMoneyMap.keySet();
        double feeSum = 0l;
        for (String repayDateStr : repayDateStrs) {
            int repayMoney = repayDateMoneyMap.get(repayDateStr);
            Date repayDate = null;
            try {
                repayDate = simpleDateFormat.parse(repayDateStr);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar repayCalendar = Calendar.getInstance();
            repayCalendar.setTime(repayDate);
            int repayDay = repayCalendar.get(Calendar.DAY_OF_YEAR);
            double repayFee = (repayDay - borrowDay) * repayMoney * FEE_RATE;
            System.out.println("repayFee:" + repayFee);
            feeSum += repayFee;
            //System.out.println("repayTotalMoney:" + (repayFee + repayMoney));
            borrowDay = repayDay;
        }
        System.out.println("feeSum:" + feeSum);
    }

    /**
     * 适用于：规律的随借随还
     *
     * @param totalBorrowCount
     * @param beginDateStr
     * @param endDateStr
     */
    private static void freeDateBackCalc(float totalBorrowCount, String beginDateStr, String endDateStr) {
        float FEE_RATE = 0.025f * 0.01f;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MMdd");
        Date beginDate = null;
        Date endDate = null;
        try {
            beginDate = simpleDateFormat.parse(beginDateStr);
            endDate = simpleDateFormat.parse(endDateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar beginCalendar = Calendar.getInstance();
        beginCalendar.setTime(beginDate);
        int beginDay = beginCalendar.get(Calendar.DAY_OF_YEAR);
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);
        int endDay = endCalendar.get(Calendar.DAY_OF_YEAR);
        int numberDaysOfUsingMoney = endDay - beginDay;
        if (numberDaysOfUsingMoney < 60) {
            float eachMoreSum = totalBorrowCount * numberDaysOfUsingMoney * FEE_RATE;
            System.out.println("total number:" + (totalBorrowCount + eachMoreSum));
        } else {
            beginCalendar.add(Calendar.MONTH, +2);
            int firstPeriodDay = beginCalendar.get(Calendar.DAY_OF_YEAR);
            double periods = Math.ceil((endDay - firstPeriodDay) / 30) + 1;
            System.out.println("periods:" + periods);

            double eachPeriodBorrowCount = totalBorrowCount / periods;
            int beginCalcFeeDay = beginDay;
            double totalPayFeeSum = 0l;

            for (int i = 1; i <= periods; i++) {
                beginCalendar.setTime(beginDate);
                beginCalendar.add(Calendar.MONTH, +(2 + (i - 1)));
                int periodDay = beginCalendar.get(Calendar.DAY_OF_YEAR);
                double eachPeriodPayFee = (totalBorrowCount - eachPeriodBorrowCount * (i - 1)) * FEE_RATE * (periodDay - beginCalcFeeDay);
                totalPayFeeSum += eachPeriodPayFee;
                System.out.println("eachBackMoney:" + (eachPeriodPayFee + eachPeriodBorrowCount));
                beginCalcFeeDay = periodDay;
            }
            System.out.println("totalPayFeeSum:" + totalPayFeeSum);
            System.out.println("totalPayMoney:" + (totalPayFeeSum + totalBorrowCount));
        }
    }


    private static void everyMonthBackCalc(float borrowCount, int periods) {
        float DAY_RATE = 0.01652667f * 0.01f;//分6期，每天还款额
        float MONTH_RATE = 0.004958001f;//分6期，每月还款额


        //float RATE = 0.025f * 0.01f;//12


        float payPerMonth = borrowCount * MONTH_RATE;


        /*float eachPay = borrowCount / periods;
        float eachMoreSum = 0f;
        if (periods == 1) {
            eachMoreSum = borrowCount * 30 * RATE;
        } else {
            //eachMoreSum = borrowCount * 30 * RATE;
            for (int i = 1; i <= periods; i++) {
                float eachMoreI = (borrowCount - (i - 1) * eachPay) * (30 + (i - 1) * 30) * RATE;
                eachMoreSum += eachMoreI;
            }
        }
        System.out.println("total number:" + (borrowCount + eachMoreSum));*/


    }


    private static void repayList() {
        List<RepayMode> itemModeList = new ArrayList() {
            {
                //0321--2107-1500-1504.5
                add(new RepayMode("2017-0402", 1504.5f));

                //0321-2354-15000-15340
                add(new RepayMode("2017-0521", 5225f));
                add(new RepayMode("2017-0621", 5077.5f));
                add(new RepayMode("2017-0721", 5037.5f));

                //0322-1014-18000-18505.52
                add(new RepayMode("2017-0521", 3270f));
                add(new RepayMode("2017-0621", 3116.26f));
                add(new RepayMode("2017-0721", 3090f));
                add(new RepayMode("2017-0803", 9029.26f));

                //0326-1132-1000-1006
                add(new RepayMode("2017-0419", 1006f));

                //0330-0744-6000-6054
                add(new RepayMode("2017-0505", 6054f));

                //0501-0812-1000-1001
                add(new RepayMode("2017-0505", 1001f));

                //0502-1447-1500-1501.12
                add(new RepayMode("2017-0505", 1501.12f));

                //0513-2256-3000-3016.5
                add(new RepayMode("2017-0605", 3016.5f));

                //0514-0212-6000-6110.76
                add(new RepayMode("2017-0621", 557f));
                add(new RepayMode("2017-0721", 541.26f));
                add(new RepayMode("2017-0731", 5012.5f));

                //0515-0923-5000-5026.26
                add(new RepayMode("2017-0605", 5026.26f));


                //0515-1225-5000-5026.26
                add(new RepayMode("2017-0605", 5026.26f));

                //0516-0006-5000-5089.79
                add(new RepayMode("2017-0621", 461.67f));
                add(new RepayMode("2017-0721", 451.05f));
                add(new RepayMode("2017-0731", 4177.07f));

                //0516-0834-5000-5089.79
                add(new RepayMode("2017-0621", 461.67f));
                add(new RepayMode("2017-0721", 451.05f));
                add(new RepayMode("2017-0731", 4177.07f));

                //0517-0922-5000-5092.72
                add(new RepayMode("2017-0621", 460.43f));
                add(new RepayMode("2017-0721", 451.05f));
                add(new RepayMode("2017-0804", 4181.24f));

                //0517-2010-5000--5176.14
                add(new RepayMode("2017-0621", 460.42f));
                add(new RepayMode("2017-0721", 451.04f));
                add(new RepayMode("2017-0821", 448.96f));
                add(new RepayMode("2017-0921", 445.73f));
                add(new RepayMode("2017-1021", 441.67f));
                add(new RepayMode("2017-1106", 2928.32f));


                //0520-1439-8500-8793.06
                add(new RepayMode("2017-0621", 776.33f));
                add(new RepayMode("2017-0721", 766.77f));
                add(new RepayMode("2017-0821", 763.22f));
                add(new RepayMode("2017-0921", 757.73f));
                add(new RepayMode("2017-1021", 750.83f));
                add(new RepayMode("2017-1106", 4978.18f));

                //0526-0917-3000-3030
                add(new RepayMode("2017-0705", 3030f));

                //0528-2306-3000-3027.76
                add(new RepayMode("2017-0705", 3027.76f));


                //0531-2122-6000-6052.50
                add(new RepayMode("2017-0705", 6052.50f));


                //0621-2002-5000-5148.74
                add(new RepayMode("2017-0721", 858.12f));
                add(new RepayMode("2017-0821", 858.12f));
                add(new RepayMode("2017-0921", 858.12f));
                add(new RepayMode("2017-1021", 858.12f));
                add(new RepayMode("2017-1121", 858.12f));
                add(new RepayMode("2017-1221", 858.14f));


                //0701-1422-6700-6899.32


                //0702-1203-500-514.88

                //0730-1821-40000-40010

                //0730-1827-40000-40010

                //0730-1832-16300-16450.78

                //----------------


                //add(new RepayMode("2017-0705", f));
                //add(new RepayMode("2017-0705", f));


            }
        };


        //对hashmap排序
        Comparator<String> comparator = new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                //return obj2.compareTo(obj1);

                // 升序排序
                return obj1.compareTo(obj2);
            }
        };
        Map<String, Float> dateCountMap = new TreeMap<>(comparator);
        for (RepayMode repayMode : itemModeList) {
            String date_str = repayMode.getDate_str();
            float repay_count = repayMode.getRepay_count();
            if (dateCountMap.containsKey(date_str)) {
                float count = dateCountMap.get(date_str);
                dateCountMap.put(date_str, count + repay_count);
            } else {
                dateCountMap.put(date_str, repay_count);
            }
        }
        System.out.println("dateCountMap:" + dateCountMap);
    }


    private static void testRepayList() {
        List<BorrowMode> itemModeList = new ArrayList() {
            {
                add(new BorrowMode(8500, 12, "2017-05-21"));
                //add(new BorrowMode(2000, 12, "2018-05-18"));
                /*add(new BorrowMode(3000, 12, "2018-04-18"));
                add(new BorrowMode(5000, 12, "2018-05-15"));
                add(new BorrowMode(4000, 12, "2018-05-19"));
                add(new BorrowMode(9000, 12, "2018-06-17"));
                add(new BorrowMode(6000, 12, "2018-06-18"));
                add(new BorrowMode(7000, 12, "2018-07-19"));
                add(new BorrowMode(10000, 12, "2018-09-15"));*/
            }
        };

        //对hashmap排序
        Comparator<String> comparator = new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                //return obj2.compareTo(obj1);

                // 升序排序
                return obj1.compareTo(obj2);
            }
        };
        Map<String, Integer> repayMap = new TreeMap<>(comparator);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (int i = 0; i < itemModeList.size(); i++) {
            BorrowMode itemMode = itemModeList.get(i);
            int count = itemMode.getCount();
            int periods = itemMode.getPeriods();
            String date_str = itemMode.getDate_str();
            Date date = null;
            try {
                date = simpleDateFormat.parse(date_str);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);


            for (int periodId = 1; periodId <= periods; periodId++) {
                calendar.add(Calendar.MONTH, +1);
                Date period_date = calendar.getTime();
                String period_date_str = simpleDateFormat.format(period_date);
                if (repayMap.containsKey(period_date_str)) {
                    int item_count = repayMap.get(period_date_str);
                    int new_item_count = item_count + count / periods;
                    repayMap.put(period_date_str, new_item_count);
                } else {
                    repayMap.put(period_date_str, count / periods);
                }
            }
        }

        System.out.print(repayMap.toString());
    }


    static class RepayMode {
        private String date_str;
        private float repay_count;

        public RepayMode(String date_str, float repay_count) {
            this.date_str = date_str;
            this.repay_count = repay_count;
        }

        public String getDate_str() {
            return date_str;
        }

        public void setDate_str(String date_str) {
            this.date_str = date_str;
        }

        public float getRepay_count() {
            return repay_count;
        }

        public void setRepay_count(float repay_count) {
            this.repay_count = repay_count;
        }
    }


    static class BorrowMode {
        private int count;
        private int periods;
        private String date_str;

        public BorrowMode(int count, int periods, String date_str) {
            this.count = count;
            this.periods = periods;
            this.date_str = date_str;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public int getPeriods() {
            return periods;
        }

        public void setPeriods(int periods) {
            this.periods = periods;
        }

        public String getDate_str() {
            return date_str;
        }

        public void setDate_str(String date_str) {
            this.date_str = date_str;
        }
    }

    private static void testGame() {
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            if (i % 2 == 1 && i % 3 == 0 && i % 4 == 1 && i % 5 == 4 && i % 6 == 3 && i % 7 == 0 && i % 8 == 1 && i % 9 == 0) {
                System.out.println("i:" + i);
            }
            if (i >= 10000) {
                break;
            }
        }
    }

    private static void testLetter() {
        System.out.println('I');// I
        System.out.println('T');// T
        System.out.println('I' + 'T');// 157
        System.out.println("I" + "T");// IT
    }


}
