package com.common.utils;

import android.Manifest;
import android.annotation.TargetApi;
import android.app.Activity;
import android.app.AppOpsManager;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Binder;
import android.os.Build;
import android.provider.Settings;
import android.support.annotation.NonNull;
import android.support.v4.app.ActivityCompat;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class PermissionUtils {

    public static boolean checkOrRequestPermission(final Activity activity, final @NonNull String permission, final int requestCode) {
        if (!checkPermission(activity, permission)) {
            ActivityCompat.requestPermissions(activity, new String[]{permission}, requestCode);
            return false;
        }
        return true;
    }

    public static boolean checkPermission(Context context, final @NonNull String permission) {
        return context.checkPermission(permission,
                Binder.getCallingPid(), Binder.getCallingUid()) == PackageManager.PERMISSION_GRANTED;
    }

    public static boolean checkInjectEventsPermission(Context context) {
        return checkPermission(context, "android.permission.INJECT_EVENTS");
    }

    public static boolean checkSystemAlertPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            return checkOp(context, 24);  //AppOpsManager.OP_SYSTEM_ALERT_WINDOW = 24
        } else {
            return checkPermission(context, Manifest.permission.SYSTEM_ALERT_WINDOW);
        }
    }

    @TargetApi(Build.VERSION_CODES.KITKAT)
    public static boolean checkOp(Context context, int op) {
        AppOpsManager manager = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
        try {
            Method method = manager.getClass().getDeclaredMethod("checkOp", int.class, int.class, String.class);
            int isAllowNum = (Integer) method.invoke(manager, op, Binder.getCallingUid(), context.getPackageName());
            return AppOpsManager.MODE_ALLOWED == isAllowNum;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 请求在其他APP上层，显示悬浮窗的权限.
     *
     * @param context
     * @return
     */
    public static boolean requestSystemAlertPermission(Context context) {
        List<Intent> intents = new ArrayList<>();
        intents.add(new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION", Uri.parse("package:" + context.getPackageName())).addFlags(Intent.FLAG_ACTIVITY_NEW_TASK));
        intents.add(new Intent("miui.intent.action.APP_PERM_EDITOR").setClassName("com.miui.securitycenter", "com.miui.permcenter.permissions.AppPermissionsEditorActivity").putExtra
                ("extra_pkgname", context.getPackageName()));
        intents.add(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.fromParts("package", context.getPackageName(), null)));
        //Toast.makeText(context, R.string.allow_system_alert_permission_tips, Toast.LENGTH_SHORT).show();
        return startMaybeActivity(context, intents);
    }

    private static boolean startMaybeActivity(Context context, List<Intent> intents) {
        for (Intent intent : intents) {
            try {
                context.startActivity(intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK));
                return true;
            } catch (ActivityNotFoundException e) {
                e.printStackTrace();
            }
        }
        return false;
    }
}
