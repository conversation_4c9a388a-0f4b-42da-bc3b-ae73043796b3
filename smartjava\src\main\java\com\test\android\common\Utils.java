package com.test.android.common;


import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.channels.FileChannel;

public class Utils {
    public static boolean writeString2Txt(String jsonString, String txtPath) {
        FileWriter fileWriter = null;
        try {
            fileWriter = new FileWriter(txtPath);
            fileWriter.write(jsonString);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                fileWriter.flush();
                fileWriter.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    public static String readStringFromTxt(String txtPath) {
        StringBuffer stringBuffer = null;
        FileReader fileReader = null;
        BufferedReader bufferedReader = null;
        try {
            fileReader = new FileReader(txtPath);
            //data is store in bufferedReader
            bufferedReader = new BufferedReader(fileReader);
            stringBuffer = new StringBuffer();
            String temp = null;
            //read every line data of bufferedReader
            while ((temp = bufferedReader.readLine()) != null) {
                stringBuffer.append(temp);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                bufferedReader.close();
                fileReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return stringBuffer.toString();
    }

    /**
     * fastest way to dirCopy file
     *
     * @param source
     * @param target
     */
    public static void nioTransferCopyFile(File source, File target) {
        FileChannel in = null;
        FileChannel out = null;
        FileInputStream inStream = null;
        FileOutputStream outStream = null;
        try {
            createEnableDir(target.getParentFile().getPath());
            inStream = new FileInputStream(source);
            outStream = new FileOutputStream(target);
            in = inStream.getChannel();
            out = outStream.getChannel();
            in.transferTo(0, in.size(), out);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                out.close();
                in.close();
                outStream.close();
                inStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 确保成功的创建一个可以访问的目录；如果原来有同名的文件，则先删除，再创建目录。
     *
     * @param folderPath
     */
    public static void createEnableDir(String folderPath) {
        File dir = new File(folderPath);
        if (dir.exists()) {
            if (!dir.isDirectory()) {
                dir.delete();
                dir.mkdirs();
            }
        } else {
            dir.mkdirs();
        }
    }

}
