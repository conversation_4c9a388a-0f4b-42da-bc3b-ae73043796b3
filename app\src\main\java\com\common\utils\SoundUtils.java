package com.common.utils;

import android.content.Context;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Build;

import com.test.android.R;


/**
 * <AUTHOR>
 * @todo
 * @time 2017/3/17 11:29
 */
public class SoundUtils {
    private SoundPool soundPool;
    private int soundId;

    public SoundUtils(Context context) {
        initSound(context);
    }

    public void initSound(Context context) {
        if (Build.VERSION.SDK_INT >= 21/*Build.VERSION_CODES.LOLLIPOP*/) {
            Logs.i("Build.VERSION.SDK_INT >= 21");
            soundPool = new SoundPool.Builder().build();
        } else {
            Logs.i("Build.VERSION.SDK_INT < 21");
            soundPool = new SoundPool(10, AudioManager.STREAM_MUSIC, 5);
            //maxStream：最大可放10个音频文件。
            //streamType：为声音的类型。如上为系统声音，其他还有音乐，铃声等等。
            //srcQuality：转化质量，默认为0。目前理解为，播放时的声音保真程度。一般对于短促的声音，影响不大。
        }
        soundId = soundPool.load(context, R.raw.music, 1);
    }

    /**
     * @todo to playSound after already initSound for a short time
     * <AUTHOR>
     * @time 2017/3/17 11:44
     */
    public void playSound() {
        soundPool.play(soundId, 0.1f,//左耳道音量【0-1】
                0.5f,//左耳道音量【0-1】
                0,//播放优先级【0表示最低优先级】
                0,//循环模式【0表示循环一次，-1表示一直循环，其他表示数字+1表示当前数字对应的循环次数】
                1//播放速度【1是正常，范围从0-2】
        );
    }

    public void stopSound(){
        //flagtest
        //to do how to stop sound.
    }
}
