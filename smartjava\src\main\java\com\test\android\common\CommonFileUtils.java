package com.test.android.common;


import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.Collection;

public class CommonFileUtils {
    /**
     * 文件重命名
     *
     * @param folderPath 文件目录
     * @param oldName    原来的文件名
     * @param newName    新文件名
     */
    public static void renameFile(String folderPath, String oldName, String newName) {
        if (!oldName.equals(newName)) {
            //新的文件名和以前文件名不同时,才有必要进行重命名
            File oldfile = new File(folderPath + "/" + oldName);
            File newfile = new File(folderPath + "/" + newName);
            if (!oldfile.exists()) {
                //重命名文件不存在
                return;
            }
            if (newfile.exists()) {
                //若在该目录下已经有一个文件和新文件名相同，则不允许重命名
                System.out.println(newName + "已经存在！");
            } else {
                oldfile.renameTo(newfile);
            }
        } else {
            System.out.println("新文件名和旧文件名相同...");
        }
    }

    public static void deleteEmptyFolder(String dir) {
        File[] files = new File(dir).listFiles();
        if (files == null || files.length == 0) {
            return;
        }
        for (File file : files) {
            if (file.isDirectory()) {
                File[] subfiles = file.listFiles();
                if (subfiles == null || subfiles.length == 0) {
                    boolean delete = file.delete();
                    System.out.println(file.getAbsolutePath() + " delete:" + String.valueOf(delete));
                } else {
                    deleteEmptyFolder(file.getAbsolutePath());
                }
            }
        }
    }


}
