package com.common.utils;


import android.content.ContentResolver;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.ThumbnailUtils;
import android.provider.MediaStore;

import java.io.File;

public class ImageUtils {

    /**
     * 根据图片的路径得到该图片在表中的ID，然后通过getThumbnailsFromImageId，获取该图片的缩略图。也可以扩展，以获取图片的其他相关信息。
     *
     * @param cr
     * @param fileName
     * @return
     */
    public static String getImageIdFromPath(ContentResolver cr, String fileName) {
        //select condition.
        String whereClause = MediaStore.Images.Media.DATA + " = '" + fileName + "'";

        //colection of results.
        Cursor cursor = cr.query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, new String[]{MediaStore.Images.Media._ID}, whereClause, null, null);
        if (cursor == null || cursor.getCount() == 0) {
            if (cursor != null)
                cursor.close();
            return null;
        }
        cursor.moveToFirst();
        //image id in image table.
        String imageId = cursor.getString(cursor.getColumnIndex(MediaStore.Images.Media._ID));
        cursor.close();
        if (imageId == null) {
            return null;
        }
        return imageId;
    }

    /**
     * 100KB
     */
    private static final int MIN_IMAGE_SIZE_TO_THUMBNAIL = 100 * 1024;



    /**
     * 指定图片的路径，来生成图片的缩略图。
     * @param imagePath
     * @return
     */
    public static Bitmap generateThumbnailFromImage(String imagePath) {
        File imageFile = new File(imagePath);
        if (!imageFile.exists()) {
            return null;
        }
        if (imageFile.length() > MIN_IMAGE_SIZE_TO_THUMBNAIL) {
            return generateThumbnail(imagePath, 0, 0, 13);
        } else {
            return generateThumbnail(imagePath, 0, 0, 1);
        }
    }

    /**
     * 根据图片的ID得到缩略图；与getImageIdFromPath结合使用，可以获取指定路径的图片，在系统媒体服务中存储的缩略图（获取系统中已保存的图片缩略图，不需要自己来生成，generateThumbnail是生成图片缩略图）。
     * 也可以扩展，以获取图片的其他相关信息。
     *
     * @param cr
     * @param imageId
     * @return
     */
    public static Bitmap getThumbnailsFromImageId(ContentResolver cr, String imageId) {
        if (imageId == null || imageId.equals(""))
            return null;
        Bitmap bitmap = null;
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inDither = false;
        options.inPreferredConfig = Bitmap.Config.ARGB_8888;
        long imageIdLong = Long.parseLong(imageId);
        //via imageid get the bimap type thumbnail in thumbnail table.
        bitmap = MediaStore.Images.Thumbnails.getThumbnail(cr, imageIdLong, MediaStore.Images.Thumbnails.MINI_KIND, options);
        return bitmap;
    }






    /**
     * 根据指定的图像路径和大小来获取缩略图。（直接通过图片文件，来生成缩略图）
     * <p>
     * getThumbnailsFromImageId为获取系统已保存的图片缩略图，而不是自己生成。
     * <p>
     * <p>
     * 此方法有两点好处：
     * 1. 使用较小的内存空间，
     * 第一次获取的bitmap实际上为null，只是为了读取宽度和高度，
     * 第二次读取的bitmap是根据比例压缩过的图像，
     * 第三次读取的bitmap是所要的缩略图。
     * 2. 缩略图对于原图像来讲没有拉伸，这里使用了2.2版本的新工具ThumbnailUtils，使
     * 用这个工具生成的图像不会被拉伸。
     *
     * @param imagePath 图像的路径
     * @param width     指定输出图像的宽度
     * @param height    指定输出图像的高度
     * @return 生成的缩略图
     */
    private static Bitmap generateThumbnail(String imagePath, int width, int height, int sampleSize) {
        Bitmap bitmap = null;
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        // 获取这个图片的宽和高，注意此处的bitmap为null
        bitmap = BitmapFactory.decodeFile(imagePath, options);
        options.inJustDecodeBounds = false; // 设为 false
        // 计算缩放比
        int h = options.outHeight;
        int w = options.outWidth;
        if (width != 0 && height != 0) {
            int beWidth = w / width;
            int beHeight = h / height;
            int be = 1;
            if (beWidth < beHeight) {
                be = beWidth;
            } else {
                be = beHeight;
            }
            if (be <= 0) {
                be = 1;
            }
            options.inSampleSize = be;
        } else if (sampleSize != 0) {
            options.inSampleSize = sampleSize;
            if (h > w) {
                height = h / sampleSize;
                width = w / sampleSize;
            } else {
                height = w / sampleSize;
                width = h / sampleSize;
            }
        }
        // 重新读入图片，读取缩放后的bitmap，注意这次要把options.inJustDecodeBounds 设为 false
        bitmap = BitmapFactory.decodeFile(imagePath, options);
        // 利用ThumbnailUtils来创建缩略图，这里要指定要缩放哪个Bitmap对象
        bitmap = ThumbnailUtils.extractThumbnail(bitmap, width, height, ThumbnailUtils.OPTIONS_RECYCLE_INPUT);
        return bitmap;
    }


}
