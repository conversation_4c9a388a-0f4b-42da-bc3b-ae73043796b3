package com.test.android.jxbycg

/**
 * 每种型号折边的长度与块数
 */
class EdgeFoldItem {
    constructor(totalLength: Float) {
        this.totalEdgeFoldLength = totalLength
    }

    constructor(length: Float, amount: Int) {
        this.edgeFoldLength = length
        this.edgeFoldAmount = amount
        this.totalEdgeFoldLength = length * amount
    }

    constructor(length: Float, amount: Int, totalLength: Float, blankingColorPlateModel: BlankingColorPlateModel) {
        this.edgeFoldLength = length
        this.edgeFoldAmount = amount
        this.totalEdgeFoldLength = totalLength
        this.blankingColorPlateModel = blankingColorPlateModel
    }

    constructor(length: Float, amount: Int, blankingAmount: Int, totalLength: Float, blankingTotalLength: Float, blankingColorPlateModel: BlankingColorPlateModel) {
        this.edgeFoldLength = length
        this.edgeFoldAmount = amount
        this.blankingEdgeFoldAmount = blankingAmount
        this.totalEdgeFoldLength = totalLength
        this.blankingTotalEdgeFoldLength = blankingTotalLength
        this.blankingColorPlateModel = blankingColorPlateModel
    }


    /**
     * 折边长度
     */
    var edgeFoldLength: Float = 0.0f

    /**
     * 折边数量
     */
    var edgeFoldAmount: Int = 0

    /**
     * 已下料的折边数量
     */
    var blankingEdgeFoldAmount: Int = 0


    /**
     * 折边总长度
     *
     * 有些型号的折边对每块的长度没有要求，只要求总长度
     */
    var totalEdgeFoldLength: Float = 0.0f


    /**
     * 已下料的折边总长度
     *
     * 有些型号的折边对每块的长度没有要求，只要求总长度
     */
    var blankingTotalEdgeFoldLength: Float = 0.0f


    /**
     * 对应的彩钢板下料
     */
    var blankingColorPlateModel = BlankingColorPlateModel()
}
