package com.test.copyDataFromOneDbToAnother;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.common.utils.Logs;

import java.util.ArrayList;
import java.util.List;

public class NicDataBaseHelper extends SQLiteOpenHelper {
    private final String TAG = this.getClass().getSimpleName().trim();

    public NicDataBaseHelper(Context context) {
        super(context, "nic.db", null, 1);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
    }

    public synchronized List<RouteInfoModel> searchRouteInfo(String startNum, String endNum) {
        List<RouteInfoModel> models = new ArrayList<RouteInfoModel>() {
        };
        final SQLiteDatabase readableDatabase = getReadableDatabase();
        final Cursor cursor = readableDatabase.rawQuery("select mac,vendor from oui limit ?,?", new String[]{startNum, endNum});
        try {
            if (cursor.moveToFirst()) {
                do {
                    RouteInfoModel routeInfoModel = new RouteInfoModel();
                    // maybe,when "select *",this is counted from 0,but not from
                    // 1
                    // and maybe,when "select date,address,body,person",this is
                    // counted from 1
                    routeInfoModel.setMac(cursor.getString(0));
                    routeInfoModel.setBrand(cursor.getString(1));

                    Logs.i(TAG, "mac-------" + cursor.getString(0));
                    Logs.i(TAG, "brand-------" + cursor.getString(1));
                    models.add(routeInfoModel);
                    // /////////// flag*
                    /*if (models.size() >= 30) {
                        return models;
					}*/
                } while (cursor.moveToNext());
                // while判断放在后面，以免第一条数据被跳过
            }
        } finally {
            cursor.close();
        }
        return models;
    }

    public int getDbSize() {
        int size = 0;
        Cursor cursor = getReadableDatabase().rawQuery("select count(*) from oui", null);
        if (cursor.moveToFirst()) {
            size = cursor.getInt(0);
        }
        cursor.close();
        return size;
    }
}