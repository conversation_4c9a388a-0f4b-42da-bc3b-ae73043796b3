<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <!--<uses-permission android:name="android.permission.WRITE_SETTINGS"/>-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>

    <application
        android:name="com.test.activity.MyApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity android:name="com.test.activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!--通过拨号盘暗码，拉起指定的activity-->
        <receiver android:name="com.test.receiver.SecretCodeReceiver">
            <intent-filter>
                <action android:name="android.provider.Telephony.SECRET_CODE"/>
                <data
                    android:host="5656"
                    android:scheme="android_secret_code"/>
            </intent-filter>
        </receiver>

        <activity android:name="com.test.activity.PreferencesActivity"/>
        <activity android:name="com.test.activity.NetWorkManagerActivity">
        </activity>
        <activity android:name="com.test.activity.SystemSettingActivity">
        </activity>
        <activity android:name="com.test.activity.UIActivity">
        </activity>
        <activity android:name="com.test.android.common.thread.ThreadActivityTest">
        </activity>
        <activity android:name="com.test.memoryleak.MemoryLeakActivity0"/>
        <activity android:name="com.test.memoryleak.MemoryLeakActivity1"/>
        <activity android:name="com.test.memoryleak.MemoryLeakActivity2"/>
        <activity android:name="com.test.memoryleak.MemoryLeakActivity5"/>
        <activity android:name="com.test.memoryleak.MemoryLeakActivity6"/>

        <activity android:name="com.test.activity.AndroidJSActivity"/>
    </application>

</manifest>