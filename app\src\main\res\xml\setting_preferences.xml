<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android" >
    <PreferenceCategory android:title="General options" >
        <CheckBoxPreference
            android:defaultValue="false"
            android:key="silent_mode"
            android:summary="Mute all sounds from this app"
            android:title="Silent Mode" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="awesome_mode"
            android:summary="Enable The Awesome Mode Feature"
            android:switchTextOff="No"
            android:switchTextOn="Yes"
            android:title="Awesome mode™" />
        <!--<EditTextPreference
            android:defaultValue="/sdcard/data/"-->

        <EditTextPreference
            android:dialogTitle="Enter directory path (eg. /sdcard/data/ )"
            android:key="custom_storage"
            android:summary="Enter the directory path where you want data to be saved. If it does not exist, it will be created."
            android:title="Custom storage location" />
    </PreferenceCategory>
</PreferenceScreen>