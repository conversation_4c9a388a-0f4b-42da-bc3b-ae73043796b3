<?xml version="1.0" encoding="utf-8"?>
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!--这个animated-selector没有任何问题，但是我们需要考虑非Lollipop设备。我们在res/drawable/selector.xml中定义一个没有动画的selector-->
    <item
        android:id="@+id/on"
        android:state_activated="true">
        <bitmap
            android:src="@drawable/ic_heart_100"/>
    </item>
    <item
        android:id="@+id/off"
        android:state_activated="false">
        <bitmap
            android:src="@drawable/ic_heart_0"/>
    </item>
    <transition
        android:drawable="@drawable/anim_heart_emptying"
        android:duration="3000"
        android:fromId="@+id/on"
        android:toId="@+id/off">
    </transition>
    <transition
        android:drawable="@drawable/anim_heart_filling"
        android:duration="3000"
        android:fromId="@+id/off"
        android:toId="@+id/on">
    </transition>
</animated-selector>