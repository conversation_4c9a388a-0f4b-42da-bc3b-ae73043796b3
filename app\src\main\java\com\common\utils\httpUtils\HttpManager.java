package com.common.utils.httpUtils;

import com.common.utils.Logs;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.zip.GZIPInputStream;

import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.CoreConnectionPNames;

/**
 * HTTP 请求类，用于管理通用的 HTTP 请求。
 * 
 * <AUTHOR>
 */
public class HttpManager {
	private static final String HTTPMETHOD_POST = "POST";
	private static final String HTTPMETHOD_GET = "GET";
	
	private static final int SET_CONNECTION_TIMEOUT = 5 * 1000;
	private static final int SET_SOCKET_TIMEOUT = 20 * 1000;
	
	/**
	 * 根据 URL 异步请求数据。
	 * 
	 * @param url
	 *            请求的地址
	 * @param method
	 *            "GET" or "POST"
	 * @param params
	 *            存放参数的容器
	 * @param encode
	 *            编码
	 * @param decode
	 *            解码
	 * @return 返回响应结果
	 * @throws MyException
	 *             如果发生错误，则以该异常抛出
	 */
	public static String openUrl(String url, String method, HttpParameters params, String encode, String decode, ArrayList<MyHeader> headerList) throws MyException {
		String result = "";
		try {
			HttpClient client = new DefaultHttpClient();
			HttpUriRequest request = null;
			ByteArrayOutputStream bos = null;
			client.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, SET_CONNECTION_TIMEOUT);
			client.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, SET_SOCKET_TIMEOUT);
			if (method.equals(HTTPMETHOD_GET)) {
				url = url + "?" + Utility.encodeUrl(params, encode);
				Logs.i("HttpManager", "openUrl get url = " + url);
				HttpGet get = new HttpGet(url);
				if (headerList != null && !headerList.isEmpty()) {
					for (MyHeader header : headerList) {
						get.setHeader(header.getKey(), header.getValue());
					}
				}
				request = get;
			} else if (method.equals(HTTPMETHOD_POST)) {
				Logs.i("HttpManager", "openUrl put url = " + url);
				HttpPost post = new HttpPost(url);
				request = post;
				byte[] data = null;
				String _contentType = params.getValue("content-type");
				
				bos = new ByteArrayOutputStream();
				
				if (_contentType != null) {
					params.remove("content-type");
					post.setHeader("Content-Type", _contentType);
				} else {
					post.setHeader("Content-Type", "application/x-www-form-urlencoded");
				}
				if (headerList != null && !headerList.isEmpty()) {
					for (MyHeader header : headerList) {
						post.setHeader(header.getKey(), header.getValue());
					}
				}
				
				String postParam = Utility.encodeParameters(params, encode);
				data = postParam.getBytes(encode);
				bos.write(data);
				
				data = bos.toByteArray();
				bos.close();
				ByteArrayEntity formEntity = new ByteArrayEntity(data);
				post.setEntity(formEntity);
			} else if (method.equals("DELETE")) {
				request = new HttpDelete(url);
			}
			HttpResponse response = client.execute(request);
			StatusLine status = response.getStatusLine();
			int statusCode = status.getStatusCode();
			
			if (statusCode != 200) {
				result = readHttpResponse(response, decode);
				throw new MyHttpException(result, statusCode);
			}
			result = readHttpResponse(response, decode);
			return result;
		} catch (IOException e) {
			throw new MyException(e);
		}
	}
	
	/**
	 * 读取HttpResponse数据
	 * 
	 * @param response
	 * @return
	 */
	private static String readHttpResponse(HttpResponse response, String decode) {
		String result = "";
		HttpEntity entity = response.getEntity();
		InputStream inputStream;
		try {
			inputStream = entity.getContent();
			ByteArrayOutputStream content = new ByteArrayOutputStream();
			
			Header header = response.getFirstHeader("Content-Encoding");
			if (header != null && header.getValue().toLowerCase().indexOf("gzip") > -1) {
				inputStream = new GZIPInputStream(inputStream);
			}
			
			int readBytes = 0;
			byte[] sBuffer = new byte[512];
			while ((readBytes = inputStream.read(sBuffer)) != -1) {
				content.write(sBuffer, 0, readBytes);
			}
			result = new String(content.toByteArray(), decode);
			return result;
		} catch (IllegalStateException e) {} catch (IOException e) {}
		return result;
	}
	
}
