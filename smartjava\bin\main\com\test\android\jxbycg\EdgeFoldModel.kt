package com.test.android.jxbycg

class EdgeFoldModel {
    constructor(sides: IntArray, edgeFoldItems: Array<EdgeFoldItem>) {
        this.sides = sides
        width = calcWidth(sides)
        this.edgeFoldItems = edgeFoldItems
    }

    constructor(sides: IntArray, width: Int, edgeFoldItems: Array<EdgeFoldItem>) {
        this.sides = sides
        this.width = width
        this.edgeFoldItems = edgeFoldItems
    }

    /**
     * 折边件所有边的长度数组
     */
    var sides: IntArray

    /**
     * 折边件的展开宽度
     */
    var width: Int

    /**
     * 每种尺寸折边的长度、数量、已下料的数量、总长度、已下料的总长度、对应的彩钢板下料
     */
    var edgeFoldItems: Array<EdgeFoldItem>

    companion object {
        /**
         * 计算折边的总宽度
         *
         * @param sides
         * @return
         */
        fun calcWidth(sides: IntArray?): Int {
            if (sides == null || sides.isEmpty()) {
                return 0
            }
            var sum = 0
            for (number in sides) {
                sum += number
            }
            return sum
        }
    }
}
