package com.common.utils.httpUtils;

import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;

import android.os.Bundle;

import com.common.utils.Logs;

/**
 * 一些方法处理，url参数获取等等。
 * 
 * <AUTHOR>
 */
public class Utility {
	
	public static String encodeUrl(HttpParameters parameters, String encode) {
		if (parameters == null) {
			return "";
		}
		StringBuilder sb = new StringBuilder();
		boolean first = true;
		for (int loc = 0; loc < parameters.size(); loc++) {
			if (first)
				first = false;
			else {
				sb.append("&");
			}
			String _key = parameters.getKey(loc);
			String _value = parameters.getValue(_key);
			if (_value == null)
				Logs.i("encodeUrl", "key:" + _key + " 's value is null");
			else {
				try {
					sb.append(URLEncoder.encode(parameters.getKey(loc), encode) + "=" + URLEncoder.encode(parameters.getValue(loc), encode));
				}
				catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}
			Logs.i("encodeUrl", sb.toString());
		}
		return sb.toString();
	}
	
	public static String encodeParameters(HttpParameters httpParams, String encode) {
		if ((httpParams == null) || (isBundleEmpty(httpParams))) {
			return "";
		}
		StringBuilder buf = new StringBuilder();
		int j = 0;
		for (int loc = 0; loc < httpParams.size(); loc++) {
			String key = httpParams.getKey(loc);
			if (j != 0)
				buf.append("&");
			try {
				buf.append(URLEncoder.encode(key, encode)).append("=").append(URLEncoder.encode(httpParams.getValue(key), encode));
			}
			catch (UnsupportedEncodingException localUnsupportedEncodingException) {}
			j++;
		}
		return buf.toString();
	}
	
	private static boolean isBundleEmpty(HttpParameters bundle) {
		return (bundle == null) || (bundle.size() == 0);
	}
	
	public static Bundle parseUrl(String url, String decode) {
		try {
			URL u = new URL(url);
			Bundle b = decodeUrl(u.getQuery(), decode);
			b.putAll(decodeUrl(u.getRef(), decode));
			return b;
		}
		catch (MalformedURLException e) {}
		return new Bundle();
	}
	
	public static Bundle decodeUrl(String s, String decode) {
		Bundle params = new Bundle();
		if (s != null) {
			String[] array = s.split("&");
			for (String parameter : array) {
				int index = parameter.indexOf("=");
				// String[] v = parameter.split("=");
				String key = parameter.substring(0, index);
				String value = parameter.substring(index + 1);
				try {
					params.putString(URLDecoder.decode(key, decode), URLDecoder.decode(value, decode));
				}
				catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}
		}
		return params;
	}
}
