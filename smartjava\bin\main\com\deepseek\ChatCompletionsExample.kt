package com.deepseek

import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChoice
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole
import com.volcengine.ark.runtime.service.ArkService
import java.lang.Exception
import java.util.ArrayList
import java.util.function.Consumer


/**
 * 这是一个示例类，展示了如何使用ArkService来完成聊天功能。
 *
 * http://volcengine.com/docs/82379/1399008
 * 模型广场---DeepSeek-R1---API调用指南
 * 快速入门-调用模型服务--火山方舟大模型服务平台-火山引擎.mhtml
 * 需要通过cmd在PC本地设置ARK_API_KEY，然后在程序中读取
 * d03419ff-d92f-4452-922c-********
 *
 * DeepSeek-R1 用量统计
 * 剩497,861/共500,000 tokens
 * 剩478,493/共500,000 tokens
 * 剩400,476/共500,000 tokens
 *
 */
object ChatCompletionsExample {
    @JvmStatic
    fun main(args: Array<String>) {
        // 从环境变量中获取API密钥
        val apiKey = System.getenv("ARK_API_KEY")

        // 创建ArkService实例
        val arkService = ArkService.builder().apiKey(apiKey).build()

        // 初始化消息列表
        val chatMessages: MutableList<ChatMessage?> = ArrayList<ChatMessage?>()

        val systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM)
            .content(
                "请根据用户输入的名字，模仿唐宋诗人的风格写两首七言绝句，要求正能量，表达欢乐、豪放、富贵、祝福;" +
                        "要求每句诗歌的第一个字依次为名字的其中一个字;每句诗歌的第一个字不要相同；诗歌的押韵方式需要符合七言绝句的规范；两首诗都以用户输入的名字作为诗歌的标题；" +
                        "以五行的形式输出，需要有标点符号；直接输出结果，无需解释；"
            )
            .build()
        chatMessages.add(systemMessage)

        // 创建用户消息
        val userMessage = ChatMessage.builder().role(ChatMessageRole.USER) // 设置消息角色为用户
            .content("齐天大圣") // 设置消息内容
            .build()
        // 将用户消息添加到消息列表
        chatMessages.add(userMessage)

        // 创建聊天完成请求
        val chatCompletionRequest = ChatCompletionRequest.builder().model("ep-20250216011843-rsfsn") // 需要替换为您的推理接入点ID
            .messages(chatMessages) // 设置消息列表
            .build()

        val startTime = System.currentTimeMillis()
        // 发送聊天完成请求并打印响应
        try {
            // 获取响应并打印每个选择的消息内容
            arkService.createChatCompletion(chatCompletionRequest).getChoices().forEach(
                Consumer { choice: ChatCompletionChoice? ->
                    println(choice!!.getMessage().getContent())
                    println()
                    println("耗时${(System.currentTimeMillis() - startTime) / 1000}秒")
                }
            )
        } catch (e: Exception) {
            println("请求失败: " + e.message)
        } finally {
            // 关闭服务执行器
            arkService.shutdownExecutor()
        }
    }
}