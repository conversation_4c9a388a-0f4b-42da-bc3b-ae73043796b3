package com.test.android.common;

import java.text.DecimalFormat;

public class Number {
    /**
     * 1.去除小数点后的零，比如：5.0 转换为 5
     * <p>
     * 2.只保留小数点后一位
     *
     * @param value
     * @return
     */
    public static String formatDouble(double value) {
        if (value % 1.0 == 0.0) {
            return String.valueOf((int) value); // 转换为整数并转为字符串
        } else {
            DecimalFormat decimalFormat = new DecimalFormat("#.##"); // 只保留小数点后一位
            return decimalFormat.format(value); // 格式化并返回字符串
        }
    }
}